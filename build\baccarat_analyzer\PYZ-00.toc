('C:\\Users\\<USER>\\Desktop\\1\\baccarat\\build\\baccarat_analyzer\\PYZ-00.pyz',
 [('_compat_pickle',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\_compat_pickle.py',
   'PYMODULE'),
  ('_compression',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\_compression.py',
   'PYMODULE'),
  ('_py_abc',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\_py_abc.py',
   'PYMODULE'),
  ('_strptime',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\_strptime.py',
   'PYMODULE'),
  ('_threading_local',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\_threading_local.py',
   'PYMODULE'),
  ('analyzer', 'C:\\Users\\<USER>\\Desktop\\1\\baccarat\\analyzer.py', 'PYMODULE'),
  ('argparse',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\argparse.py',
   'PYMODULE'),
  ('ast',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\ast.py',
   'PYMODULE'),
  ('base64',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\base64.py',
   'PYMODULE'),
  ('bisect',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\bisect.py',
   'PYMODULE'),
  ('bz2',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\bz2.py',
   'PYMODULE'),
  ('calendar',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\calendar.py',
   'PYMODULE'),
  ('configparser',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\configparser.py',
   'PYMODULE'),
  ('contextlib',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\contextlib.py',
   'PYMODULE'),
  ('copy',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\copy.py',
   'PYMODULE'),
  ('csv',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\csv.py',
   'PYMODULE'),
  ('dataclasses',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\dataclasses.py',
   'PYMODULE'),
  ('datetime',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\datetime.py',
   'PYMODULE'),
  ('dis',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\dis.py',
   'PYMODULE'),
  ('drawing', 'C:\\Users\\<USER>\\Desktop\\1\\baccarat\\drawing.py', 'PYMODULE'),
  ('email',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\email\\__init__.py',
   'PYMODULE'),
  ('email._encoded_words',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\email\\_encoded_words.py',
   'PYMODULE'),
  ('email._header_value_parser',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\email\\_header_value_parser.py',
   'PYMODULE'),
  ('email._parseaddr',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\email\\_parseaddr.py',
   'PYMODULE'),
  ('email._policybase',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\email\\_policybase.py',
   'PYMODULE'),
  ('email.base64mime',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\email\\base64mime.py',
   'PYMODULE'),
  ('email.charset',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\email\\charset.py',
   'PYMODULE'),
  ('email.contentmanager',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\email\\contentmanager.py',
   'PYMODULE'),
  ('email.encoders',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\email\\encoders.py',
   'PYMODULE'),
  ('email.errors',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\email\\errors.py',
   'PYMODULE'),
  ('email.feedparser',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\email\\feedparser.py',
   'PYMODULE'),
  ('email.generator',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\email\\generator.py',
   'PYMODULE'),
  ('email.header',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\email\\header.py',
   'PYMODULE'),
  ('email.headerregistry',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\email\\headerregistry.py',
   'PYMODULE'),
  ('email.iterators',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\email\\iterators.py',
   'PYMODULE'),
  ('email.message',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\email\\message.py',
   'PYMODULE'),
  ('email.parser',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\email\\parser.py',
   'PYMODULE'),
  ('email.policy',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\email\\policy.py',
   'PYMODULE'),
  ('email.quoprimime',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\email\\quoprimime.py',
   'PYMODULE'),
  ('email.utils',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\email\\utils.py',
   'PYMODULE'),
  ('fnmatch',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\fnmatch.py',
   'PYMODULE'),
  ('getopt',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\getopt.py',
   'PYMODULE'),
  ('gettext',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\gettext.py',
   'PYMODULE'),
  ('gui', 'C:\\Users\\<USER>\\Desktop\\1\\baccarat\\gui.py', 'PYMODULE'),
  ('gzip',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\gzip.py',
   'PYMODULE'),
  ('hashlib',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\hashlib.py',
   'PYMODULE'),
  ('importlib',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\importlib\\__init__.py',
   'PYMODULE'),
  ('importlib._bootstrap',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\importlib\\_bootstrap.py',
   'PYMODULE'),
  ('importlib._bootstrap_external',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\importlib\\_bootstrap_external.py',
   'PYMODULE'),
  ('importlib.abc',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\importlib\\abc.py',
   'PYMODULE'),
  ('importlib.machinery',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\importlib\\machinery.py',
   'PYMODULE'),
  ('importlib.metadata',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\importlib\\metadata.py',
   'PYMODULE'),
  ('importlib.util',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\importlib\\util.py',
   'PYMODULE'),
  ('inspect',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\inspect.py',
   'PYMODULE'),
  ('logging',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\logging\\__init__.py',
   'PYMODULE'),
  ('lzma',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\lzma.py',
   'PYMODULE'),
  ('models', 'C:\\Users\\<USER>\\Desktop\\1\\baccarat\\models.py', 'PYMODULE'),
  ('opcode',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\opcode.py',
   'PYMODULE'),
  ('optparse',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\optparse.py',
   'PYMODULE'),
  ('pathlib',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\pathlib.py',
   'PYMODULE'),
  ('pickle',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\pickle.py',
   'PYMODULE'),
  ('pprint',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\pprint.py',
   'PYMODULE'),
  ('profit_calculator',
   'C:\\Users\\<USER>\\Desktop\\1\\baccarat\\profit_calculator.py',
   'PYMODULE'),
  ('py_compile',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\py_compile.py',
   'PYMODULE'),
  ('quopri',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\quopri.py',
   'PYMODULE'),
  ('random',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\random.py',
   'PYMODULE'),
  ('selectors',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\selectors.py',
   'PYMODULE'),
  ('shutil',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\shutil.py',
   'PYMODULE'),
  ('signal',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\signal.py',
   'PYMODULE'),
  ('socket',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\socket.py',
   'PYMODULE'),
  ('string',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\string.py',
   'PYMODULE'),
  ('stringprep',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\stringprep.py',
   'PYMODULE'),
  ('subprocess',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\subprocess.py',
   'PYMODULE'),
  ('tarfile',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\tarfile.py',
   'PYMODULE'),
  ('textwrap',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\textwrap.py',
   'PYMODULE'),
  ('threading',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\threading.py',
   'PYMODULE'),
  ('tkinter',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\tkinter\\__init__.py',
   'PYMODULE'),
  ('tkinter.commondialog',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\tkinter\\commondialog.py',
   'PYMODULE'),
  ('tkinter.constants',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\tkinter\\constants.py',
   'PYMODULE'),
  ('tkinter.dialog',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\tkinter\\dialog.py',
   'PYMODULE'),
  ('tkinter.filedialog',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\tkinter\\filedialog.py',
   'PYMODULE'),
  ('tkinter.messagebox',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\tkinter\\messagebox.py',
   'PYMODULE'),
  ('tkinter.simpledialog',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\tkinter\\simpledialog.py',
   'PYMODULE'),
  ('tkinter.ttk',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\tkinter\\ttk.py',
   'PYMODULE'),
  ('token',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\token.py',
   'PYMODULE'),
  ('tokenize',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\tokenize.py',
   'PYMODULE'),
  ('tracemalloc',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\tracemalloc.py',
   'PYMODULE'),
  ('typing',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\typing.py',
   'PYMODULE'),
  ('urllib',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\urllib\\__init__.py',
   'PYMODULE'),
  ('urllib.parse',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\urllib\\parse.py',
   'PYMODULE'),
  ('uu',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\uu.py',
   'PYMODULE'),
  ('zipfile',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\zipfile.py',
   'PYMODULE')])
