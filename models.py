"""
百家乐分析器数据模型模块
定义游戏结果、路图数据、预测数据等数据结构
"""

from dataclasses import dataclass, field
from typing import List, Optional, Dict, Any
from enum import Enum


class GameResult(Enum):
    """游戏结果枚举"""
    BANKER = 'B'  # 庄
    PLAYER = 'P'  # 闲
    TIE = 'T'     # 和


class PatternType(Enum):
    """模式类型枚举"""
    LIAN = 'lian'  # 连式
    TIAO = 'tiao'  # 跳式


class BlindGateDirection(Enum):
    """盲门预测方向枚举"""
    LIAN = 'lian'  # 打连：预测值为前一口的值
    ZHENG = 'zheng'  # 打正：预测值为当前株第一口的值
    FAN = 'fan'  # 打反：预测值为当前株第一口的相反值


class BlindGateStatus(Enum):
    """盲门状态枚举"""
    WAITING = 'waiting'  # 等待中（还没有足够的株来确定盲门）
    TEMPORARY = 'temporary'  # 临时盲门（需要继续观察）
    CONFIRMED = 'confirmed'  # 确定盲门
    PAUSED = 'paused'  # 暂停（遇到盲门对应的分类）
    STOPPED = 'stopped'  # 停止（遇到1,3,4组合）


@dataclass
class Prediction:
    """单个预测数据"""
    position: int  # 预测位置（2或3）
    predicted_value: GameResult  # 预测值
    actual_value: Optional[GameResult] = None  # 实际值


@dataclass
class ColumnPrediction:
    """列预测数据"""
    column_index: int  # 列索引
    pattern_type: PatternType  # 模式类型
    reference_column: int  # 参考列索引
    predictions: List[Prediction] = field(default_factory=list)  # 预测列表


@dataclass
class BlindGatePrediction:
    """盲门预测数据"""
    position: int  # 预测位置（2或3）
    predicted_value: GameResult  # 预测值
    direction: BlindGateDirection  # 预测方向
    actual_value: Optional[GameResult] = None  # 实际值


@dataclass
class BlindGateColumnPrediction:
    """盲门列预测数据"""
    column_index: int  # 列索引
    column_classification: int  # 列分类（1-4）
    direction: BlindGateDirection  # 预测方向
    predictions: List[BlindGatePrediction] = field(default_factory=list)  # 预测列表


@dataclass
class BlindGateState:
    """盲门状态数据"""
    status: BlindGateStatus = BlindGateStatus.WAITING  # 盲门状态
    blind_gate_number: Optional[int] = None  # 盲门数字（1-4）
    direction: Optional[BlindGateDirection] = None  # 预测方向
    observed_classifications: List[int] = field(default_factory=list)  # 已观察到的分类序列
    unique_classifications: set = field(default_factory=set)  # 已出现的不同分类

    def add_classification(self, classification: int):
        """添加新的分类"""
        self.observed_classifications.append(classification)
        self.unique_classifications.add(classification)

    def get_classification_count(self) -> int:
        """获取已观察的分类数量"""
        return len(self.observed_classifications)

    def get_unique_count(self) -> int:
        """获取不同分类的数量"""
        return len(self.unique_classifications)

    def clear(self):
        """清空状态"""
        self.status = BlindGateStatus.WAITING
        self.blind_gate_number = None
        self.direction = None
        self.observed_classifications.clear()
        self.unique_classifications.clear()


@dataclass
class RoadData:
    """路图数据"""
    columns: List[List[GameResult]] = field(default_factory=list)  # 路图列数据

    def add_column(self, column: List[GameResult]):
        """添加一列数据"""
        self.columns.append(column)

    def clear(self):
        """清空数据"""
        self.columns.clear()

    def get_last_column(self) -> Optional[List[GameResult]]:
        """获取最后一列"""
        return self.columns[-1] if self.columns else None

    def get_column_count(self) -> int:
        """获取列数"""
        return len(self.columns)


@dataclass
class GameState:
    """游戏状态数据"""
    # 基础数据
    results: List[GameResult] = field(default_factory=list)  # 游戏结果
    big_road: RoadData = field(default_factory=RoadData)  # 大路图

    # 三条三珠路
    three_column_road_1: RoadData = field(default_factory=RoadData)  # 第一条路
    three_column_road_2: RoadData = field(default_factory=RoadData)  # 第二条路
    three_column_road_3: RoadData = field(default_factory=RoadData)  # 第三条路

    # 普通连跳预测数据
    predictions_1: List[ColumnPrediction] = field(default_factory=list)  # 第一条路预测
    predictions_2: List[ColumnPrediction] = field(default_factory=list)  # 第二条路预测
    predictions_3: List[ColumnPrediction] = field(default_factory=list)  # 第三条路预测

    # 盲门预测数据
    blind_gate_state_1: BlindGateState = field(default_factory=BlindGateState)  # 第一条路盲门状态
    blind_gate_state_2: BlindGateState = field(default_factory=BlindGateState)  # 第二条路盲门状态
    blind_gate_state_3: BlindGateState = field(default_factory=BlindGateState)  # 第三条路盲门状态
    blind_gate_predictions_1: List[BlindGateColumnPrediction] = field(default_factory=list)  # 第一条路盲门预测
    blind_gate_predictions_2: List[BlindGateColumnPrediction] = field(default_factory=list)  # 第二条路盲门预测
    blind_gate_predictions_3: List[BlindGateColumnPrediction] = field(default_factory=list)  # 第三条路盲门预测

    # 正负路数据
    positive_negative_road_1: List[str] = field(default_factory=list)  # 第一条路正负路
    positive_negative_road_2: List[str] = field(default_factory=list)  # 第二条路正负路
    positive_negative_road_3: List[str] = field(default_factory=list)  # 第三条路正负路
    blind_gate_positive_negative_road_1: List[str] = field(default_factory=list)  # 第一条路盲门正负路
    blind_gate_positive_negative_road_2: List[str] = field(default_factory=list)  # 第二条路盲门正负路
    blind_gate_positive_negative_road_3: List[str] = field(default_factory=list)  # 第三条路盲门正负路
    
    def add_result(self, result: GameResult):
        """添加游戏结果"""
        self.results.append(result)
    
    def get_filtered_results(self) -> List[GameResult]:
        """获取过滤后的结果（排除和）"""
        return [r for r in self.results if r != GameResult.TIE]
    
    def clear_all(self):
        """清空所有数据"""
        self.results.clear()
        self.big_road.clear()
        self.three_column_road_1.clear()
        self.three_column_road_2.clear()
        self.three_column_road_3.clear()
        self.predictions_1.clear()
        self.predictions_2.clear()
        self.predictions_3.clear()
        self.blind_gate_state_1.clear()
        self.blind_gate_state_2.clear()
        self.blind_gate_state_3.clear()
        self.blind_gate_predictions_1.clear()
        self.blind_gate_predictions_2.clear()
        self.blind_gate_predictions_3.clear()
        self.positive_negative_road_1.clear()
        self.positive_negative_road_2.clear()
        self.positive_negative_road_3.clear()
        self.blind_gate_positive_negative_road_1.clear()
        self.blind_gate_positive_negative_road_2.clear()
        self.blind_gate_positive_negative_road_3.clear()


@dataclass
class LadderState:
    """楼梯缆状态"""
    levels: List[int] = field(default_factory=lambda: [20, 40, 60, 80, 100, 120, 140, 160, 180, 200])
    current_level: int = 0  # 当前级别索引
    
    def get_current_bet(self) -> int:
        """获取当前下注金额"""
        return self.levels[self.current_level]
    
    def level_up(self):
        """升级"""
        if self.current_level < len(self.levels) - 1:
            self.current_level += 1
    
    def level_down(self):
        """降级"""
        if self.current_level > 0:
            self.current_level -= 1
    
    def reset(self):
        """重置到第一级"""
        self.current_level = 0


@dataclass
class ProfitState:
    """收益状态"""
    # 三条路的普通连跳预测收益
    profit_1: int = 0
    profit_2: int = 0
    profit_3: int = 0

    # 三条路的盲门预测收益
    blind_gate_profit_1: int = 0
    blind_gate_profit_2: int = 0
    blind_gate_profit_3: int = 0

    # 三条路的普通连跳预测楼梯缆级别
    ladder_level_1: int = 0
    ladder_level_2: int = 0
    ladder_level_3: int = 0

    # 三条路的盲门预测楼梯缆级别
    blind_gate_ladder_level_1: int = 0
    blind_gate_ladder_level_2: int = 0
    blind_gate_ladder_level_3: int = 0

    # 总收益
    total_profit: int = 0
    blind_gate_total_profit: int = 0

    def clear_all(self):
        """清空所有收益数据"""
        self.profit_1 = 0
        self.profit_2 = 0
        self.profit_3 = 0
        self.blind_gate_profit_1 = 0
        self.blind_gate_profit_2 = 0
        self.blind_gate_profit_3 = 0
        self.ladder_level_1 = 0
        self.ladder_level_2 = 0
        self.ladder_level_3 = 0
        self.blind_gate_ladder_level_1 = 0
        self.blind_gate_ladder_level_2 = 0
        self.blind_gate_ladder_level_3 = 0
        self.total_profit = 0
        self.blind_gate_total_profit = 0


@dataclass
class HistoryState:
    """历史状态（用于撤回）"""
    game_state: GameState
    profit_state: ProfitState
    ladder_state: LadderState
    
    @classmethod
    def create_snapshot(cls, game_state: GameState, profit_state: ProfitState, ladder_state: LadderState):
        """创建当前状态快照"""
        import copy
        return cls(
            game_state=copy.deepcopy(game_state),
            profit_state=copy.deepcopy(profit_state),
            ladder_state=copy.deepcopy(ladder_state)
        )


@dataclass
class PatternInfo:
    """模式信息"""
    number: Optional[int] = None  # 模式数字（1-4）
    type_text: Optional[str] = None  # 正反类型
    connection_text: Optional[str] = None  # 连跳类型
    
    @classmethod
    def analyze_column(cls, column: List[GameResult]):
        """分析列的模式信息"""
        if len(column) != 3:
            return cls()
        
        # 转换为字符串便于比较
        col_str = [r.value for r in column]
        
        # 计算模式数字
        number = None
        if col_str[0] == col_str[1] == col_str[2]:  # BBB 或 PPP
            number = 1
        elif col_str[0] == col_str[1] and col_str[0] != col_str[2]:  # BBP 或 PPB
            number = 2
        elif col_str[1] == col_str[2] and col_str[0] != col_str[1]:  # BPP 或 PBB
            number = 3
        elif col_str[0] == col_str[2] and col_str[0] != col_str[1]:  # BPB 或 PBP
            number = 4
        
        # 计算正反类型
        type_text = None
        if col_str[0] == col_str[1] == col_str[2]:  # BBB 或 PPP
            type_text = "正"
        elif col_str[1] == col_str[2] and col_str[0] != col_str[1]:  # BPP 或 PBB
            type_text = "反"
        else:  # 其它情况
            type_text = "正反"
        
        # 计算连跳类型
        connection_text = None
        if col_str[0] == col_str[1] == col_str[2]:  # BBB 或 PPP
            connection_text = "连"
        elif col_str[0] == col_str[2] and col_str[0] != col_str[1]:  # BPB 或 PBP
            connection_text = "跳"
        else:  # 其它情况
            connection_text = "连跳"
        
        return cls(number=number, type_text=type_text, connection_text=connection_text)
