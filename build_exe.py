#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
PyInstaller打包脚本
用于将百家乐分析器打包成可执行文件
"""

import os
import sys
import subprocess
import shutil
from pathlib import Path

def check_pyinstaller():
    """检查PyInstaller是否已安装"""
    try:
        import PyInstaller
        print(f"✅ PyInstaller已安装，版本：{PyInstaller.__version__}")
        return True
    except ImportError:
        print("❌ PyInstaller未安装")
        return False

def install_pyinstaller():
    """安装PyInstaller"""
    print("正在安装PyInstaller...")
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", "pyinstaller"])
        print("✅ PyInstaller安装成功")
        return True
    except subprocess.CalledProcessError:
        print("❌ PyInstaller安装失败")
        return False

def clean_build_dirs():
    """清理之前的构建目录"""
    dirs_to_clean = ["build", "dist", "__pycache__"]
    for dir_name in dirs_to_clean:
        if os.path.exists(dir_name):
            print(f"清理目录：{dir_name}")
            shutil.rmtree(dir_name)
    
    # 清理.spec文件
    spec_files = list(Path(".").glob("*.spec"))
    for spec_file in spec_files:
        print(f"删除spec文件：{spec_file}")
        spec_file.unlink()

def create_spec_file():
    """创建PyInstaller的spec文件"""
    spec_content = '''# -*- mode: python ; coding: utf-8 -*-

block_cipher = None

a = Analysis(
    ['main.py'],
    pathex=[],
    binaries=[],
    datas=[],
    hiddenimports=[
        'tkinter',
        'tkinter.ttk',
        'tkinter.messagebox',
        'tkinter.filedialog',
        'enum',
        'dataclasses',
        'typing',
        'collections',
    ],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.zipfiles,
    a.datas,
    [],
    name='百家乐分析器',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=False,  # 不显示控制台窗口
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon=None,  # 可以添加图标文件路径
)
'''
    
    with open("baccarat_analyzer.spec", "w", encoding="utf-8") as f:
        f.write(spec_content)
    
    print("✅ 创建spec文件：baccarat_analyzer.spec")

def build_executable():
    """构建可执行文件"""
    print("开始构建可执行文件...")
    
    try:
        # 使用spec文件构建
        cmd = [
            "pyinstaller",
            "--clean",
            "baccarat_analyzer.spec"
        ]
        
        print(f"执行命令：{' '.join(cmd)}")
        result = subprocess.run(cmd, capture_output=True, text=True, encoding='utf-8')
        
        if result.returncode == 0:
            print("✅ 构建成功！")
            print(f"可执行文件位置：dist/百家乐分析器.exe")
            return True
        else:
            print("❌ 构建失败")
            print("错误输出：")
            print(result.stderr)
            return False
            
    except Exception as e:
        print(f"❌ 构建过程中出现异常：{e}")
        return False

def check_dependencies():
    """检查项目依赖"""
    print("检查项目依赖...")
    
    required_files = [
        "main.py",
        "gui.py", 
        "analyzer.py",
        "models.py",
        "profit_calculator.py"
    ]
    
    missing_files = []
    for file in required_files:
        if not os.path.exists(file):
            missing_files.append(file)
    
    if missing_files:
        print(f"❌ 缺少必要文件：{missing_files}")
        return False
    
    print("✅ 所有必要文件都存在")
    return True

def main():
    """主函数"""
    print("=" * 60)
    print("百家乐分析器 - PyInstaller打包脚本")
    print("=" * 60)
    
    # 检查项目依赖
    if not check_dependencies():
        print("请确保所有必要文件都在当前目录中")
        return False
    
    # 检查PyInstaller
    if not check_pyinstaller():
        if not install_pyinstaller():
            return False
    
    # 清理构建目录
    clean_build_dirs()
    
    # 创建spec文件
    create_spec_file()
    
    # 构建可执行文件
    if build_executable():
        print("\n" + "=" * 60)
        print("🎉 打包完成！")
        print("=" * 60)
        print("可执行文件位置：dist/百家乐分析器.exe")
        print("您可以将整个dist目录分发给其他用户")
        print("=" * 60)
        return True
    else:
        print("\n" + "=" * 60)
        print("❌ 打包失败")
        print("=" * 60)
        return False

if __name__ == "__main__":
    success = main()
    
    # 等待用户按键
    input("\n按回车键退出...")
    
    sys.exit(0 if success else 1)
