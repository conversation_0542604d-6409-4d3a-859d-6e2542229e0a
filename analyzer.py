"""
百家乐分析器业务逻辑模块
实现大路图计算、三珠路计算、预测算法等核心业务逻辑
"""

from typing import List, Optional
from models import (
    GameResult, GameState, RoadData, ColumnPrediction,
    Prediction, PatternType, PatternInfo, BlindGateState,
    BlindGateStatus, BlindGateDirection, BlindGatePrediction,
    BlindGateColumnPrediction
)


class BaccaratAnalyzer:
    """百家乐分析器核心业务逻辑"""
    
    def __init__(self):
        self.game_state = GameState()
    
    def add_result(self, result: GameResult):
        """添加游戏结果并更新所有数据"""
        self.game_state.add_result(result)
        self.update_all_data()
    
    def update_all_data(self):
        """更新所有数据结构"""
        self.update_big_road()
        self.update_three_column_roads()
        self.update_all_predictions()
        self.update_all_blind_gate_predictions()
        self.update_all_positive_negative_roads()
    
    def update_big_road(self):
        """更新大路图数据：连续相同向下，不同向右（忽略和）"""
        filtered_results = self.game_state.get_filtered_results()

        if not filtered_results:
            self.game_state.big_road.clear()
            return

        self.game_state.big_road.clear()
        current_column = [filtered_results[0]]

        for i in range(1, len(filtered_results)):
            if filtered_results[i] == filtered_results[i-1]:
                current_column.append(filtered_results[i])  # 相同结果向下
            else:
                self.game_state.big_road.add_column(current_column)  # 不同结果新列
                current_column = [filtered_results[i]]

        self.game_state.big_road.add_column(current_column)
    
    def update_three_column_roads(self):
        """更新三条三珠路数据"""
        filtered_results = self.game_state.get_filtered_results()
        
        # 第一条路：从第1口开始
        self.game_state.three_column_road_1.clear()
        for i in range(0, len(filtered_results), 3):
            column = filtered_results[i:i+3]
            if column:
                self.game_state.three_column_road_1.add_column(column)
        
        # 第二条路：从第2口开始
        self.game_state.three_column_road_2.clear()
        if len(filtered_results) > 1:
            for i in range(1, len(filtered_results), 3):
                column = filtered_results[i:i+3]
                if column:
                    self.game_state.three_column_road_2.add_column(column)
        
        # 第三条路：从第3口开始
        self.game_state.three_column_road_3.clear()
        if len(filtered_results) > 2:
            for i in range(2, len(filtered_results), 3):
                column = filtered_results[i:i+3]
                if column:
                    self.game_state.three_column_road_3.add_column(column)
    
    def update_all_predictions(self):
        """更新所有三条路的预测数据"""
        self.game_state.predictions_1 = self.update_predictions_for_road(
            self.game_state.three_column_road_1
        )
        self.game_state.predictions_2 = self.update_predictions_for_road(
            self.game_state.three_column_road_2
        )
        self.game_state.predictions_3 = self.update_predictions_for_road(
            self.game_state.three_column_road_3
        )
    
    def update_predictions_for_road(self, road_data: RoadData) -> List[ColumnPrediction]:
        """为指定路更新预测数据：基于连式和跳式进行预测"""
        predictions = []

        # 检查是否应该停止预测（分类1和4相邻约束）
        should_stop_new_predictions = self.should_stop_predictions_due_to_adjacent_1_4(road_data)

        # 如果约束触发，只处理已确认的预测，不生成新预测
        if should_stop_new_predictions:
            # 找到约束触发时的完整列数（用于确定哪些预测是"已存在的"）
            complete_columns_when_constraint_triggered = self.get_complete_columns_when_constraint_triggered(road_data)
            max_allowed_column = complete_columns_when_constraint_triggered - 1  # 最后一个允许预测的列索引
        else:
            max_allowed_column = float('inf')  # 无限制

        for col_idx in range(1, road_data.get_column_count()):
            current_col = road_data.columns[col_idx]

            # 如果约束触发，只处理约束触发前就存在的列
            if should_stop_new_predictions and col_idx > max_allowed_column:
                continue  # 跳过约束触发后新增的列

            # 寻找参考列（连式或跳式）
            reference_pattern = self.find_reference_pattern_for_road(road_data, col_idx)

            if reference_pattern:
                pattern_type = reference_pattern['type']
                column_predictions = []

                # 预测第2口
                if len(current_col) >= 1:
                    if pattern_type == PatternType.LIAN:
                        # 连式：第2口跟第1口一个方向
                        predicted_2nd = current_col[0]
                    else:  # pattern_type == PatternType.TIAO
                        # 跳式：第2口跟第1口反方向
                        predicted_2nd = GameResult.PLAYER if current_col[0] == GameResult.BANKER else GameResult.BANKER

                    column_predictions.append(Prediction(
                        position=2,
                        predicted_value=predicted_2nd,
                        actual_value=current_col[1] if len(current_col) >= 2 else None
                    ))

                # 预测第3口（只有第2口预测错了才预测第3口）
                if len(current_col) >= 2:
                    # 检查第2口预测是否正确
                    second_prediction_correct = (column_predictions[0].predicted_value == current_col[1])

                    if not second_prediction_correct:
                        # 第2口预测错了，才预测第3口
                        if pattern_type == PatternType.LIAN:
                            # 连式：第3口跟第2口一个方向
                            predicted_3rd = current_col[1]
                        else:  # pattern_type == PatternType.TIAO
                            # 跳式：第3口跟第2口反方向
                            predicted_3rd = GameResult.PLAYER if current_col[1] == GameResult.BANKER else GameResult.BANKER

                        column_predictions.append(Prediction(
                            position=3,
                            predicted_value=predicted_3rd,
                            actual_value=current_col[2] if len(current_col) >= 3 else None
                        ))

                predictions.append(ColumnPrediction(
                    column_index=col_idx,
                    pattern_type=pattern_type,
                    reference_column=reference_pattern['column_index'],
                    predictions=column_predictions
                ))
        
        return predictions

    def should_stop_predictions_due_to_adjacent_1_4(self, road_data: RoadData) -> bool:
        """检查是否应该因为分类1和4相邻而停止预测"""
        # 获取所有完整列的分类
        complete_columns = [col for col in road_data.columns if len(col) == 3]
        if len(complete_columns) < 2:
            return False  # 至少需要2列才能检查相邻

        # 计算所有完整列的分类
        classifications = []
        for col in complete_columns:
            classification = self.classify_column_for_blind_gate(col)
            classifications.append(classification)

        # 检查是否有相邻的分类1和4
        for i in range(len(classifications) - 1):
            current_class = classifications[i]
            next_class = classifications[i + 1]

            # 检查相邻的分类是否为1和4（任意顺序）
            if (current_class == 1 and next_class == 4) or (current_class == 4 and next_class == 1):
                return True

        return False

    def get_complete_columns_when_constraint_triggered(self, road_data: RoadData) -> int:
        """获取约束触发时的完整列数"""
        # 约束触发需要至少2个完整列
        # 当第2个完整列完成时，约束就会触发
        # 所以约束触发时的完整列数就是2
        complete_columns = [col for col in road_data.columns if len(col) == 3]
        if len(complete_columns) >= 2:
            # 检查前两列是否触发约束
            class1 = self.classify_column_for_blind_gate(complete_columns[0])
            class2 = self.classify_column_for_blind_gate(complete_columns[1])
            if (class1 == 1 and class2 == 4) or (class1 == 4 and class2 == 1):
                return 2  # 约束在第2列完成时触发

        # 如果前两列没有触发，检查后续相邻列
        for i in range(len(complete_columns) - 1):
            class_curr = self.classify_column_for_blind_gate(complete_columns[i])
            class_next = self.classify_column_for_blind_gate(complete_columns[i + 1])
            if (class_curr == 1 and class_next == 4) or (class_curr == 4 and class_next == 1):
                return i + 2  # 约束在第(i+2)列完成时触发

        return len(complete_columns)  # 如果没有触发约束，返回当前完整列数

    def find_reference_pattern_for_road(self, road_data: RoadData, current_col_idx: int) -> Optional[dict]:
        """为指定路寻找参考模式（连式或跳式）"""
        # 从前一列开始往前寻找，直到第一列
        for ref_col_idx in range(current_col_idx - 1, -1, -1):
            ref_col = road_data.columns[ref_col_idx]
            
            # 只检查满3个的列
            if len(ref_col) == 3:
                pattern_type = self.identify_pattern(ref_col)
                if pattern_type:
                    return {
                        'type': pattern_type,
                        'column_index': ref_col_idx
                    }
        
        return None
    
    def identify_pattern(self, column: List[GameResult]) -> Optional[PatternType]:
        """识别列的模式类型"""
        if len(column) != 3:
            return None
        
        # 连式：三个都相同 (红红红/蓝蓝蓝)
        if column[0] == column[1] == column[2]:
            return PatternType.LIAN
        
        # 跳式：第1和第3相同，第2不同 (红蓝红/蓝红蓝)
        if column[0] == column[2] and column[0] != column[1]:
            return PatternType.TIAO
        
        # 其他情况不是标准模式
        return None
    
    def update_all_positive_negative_roads(self):
        """更新所有三条路的正负路数据"""
        self.game_state.positive_negative_road_1 = self.update_positive_negative_road_for_predictions(
            self.game_state.predictions_1
        )
        self.game_state.positive_negative_road_2 = self.update_positive_negative_road_for_predictions(
            self.game_state.predictions_2
        )
        self.game_state.positive_negative_road_3 = self.update_positive_negative_road_for_predictions(
            self.game_state.predictions_3
        )

        # 更新盲门正负路数据
        self.game_state.blind_gate_positive_negative_road_1 = self.update_blind_gate_positive_negative_road_for_predictions(
            self.game_state.blind_gate_predictions_1
        )
        self.game_state.blind_gate_positive_negative_road_2 = self.update_blind_gate_positive_negative_road_for_predictions(
            self.game_state.blind_gate_predictions_2
        )
        self.game_state.blind_gate_positive_negative_road_3 = self.update_blind_gate_positive_negative_road_for_predictions(
            self.game_state.blind_gate_predictions_3
        )
    
    def update_positive_negative_road_for_predictions(self, predictions: List[ColumnPrediction]) -> List[str]:
        """为指定预测数据更新正负路数据：预测对了记+，错了记-"""
        positive_negative_road = []
        
        for pred_info in predictions:
            for pred in pred_info.predictions:
                if pred.actual_value is not None:  # 只记录已确认的预测
                    if pred.predicted_value == pred.actual_value:
                        positive_negative_road.append('+')  # 预测正确
                    else:
                        positive_negative_road.append('-')  # 预测错误
        
        return positive_negative_road
    
    def get_next_prediction_for_road(self, road_index: int) -> Optional[dict]:
        """获取指定路的下一个预测信息"""
        if road_index == 1:
            predictions = self.game_state.predictions_1
        elif road_index == 2:
            predictions = self.game_state.predictions_2
        elif road_index == 3:
            predictions = self.game_state.predictions_3
        else:
            return None
        
        for pred_info in predictions:
            for pred in pred_info.predictions:
                if pred.actual_value is None:  # 找到第一个未确认的预测
                    return {
                        'value': pred.predicted_value,
                        'pattern': pred_info.pattern_type
                    }
        return None
    
    def calculate_accuracy_for_road(self, road_index: int) -> dict:
        """计算指定路的准确率"""
        if road_index == 1:
            predictions = self.game_state.predictions_1
        elif road_index == 2:
            predictions = self.game_state.predictions_2
        elif road_index == 3:
            predictions = self.game_state.predictions_3
        else:
            return {'correct': 0, 'total': 0, 'percentage': 0.0}
        
        total = correct = 0
        
        for pred_info in predictions:
            for pred in pred_info.predictions:
                if pred.actual_value is not None:
                    total += 1
                    if pred.predicted_value == pred.actual_value:
                        correct += 1
        
        percentage = (correct / total * 100) if total > 0 else 0.0
        return {'correct': correct, 'total': total, 'percentage': percentage}
    
    def clear_all_data(self):
        """清空所有数据"""
        self.game_state.clear_all()
    
    def get_game_state(self) -> GameState:
        """获取游戏状态"""
        return self.game_state

    # ==================== 盲门预测相关方法 ====================

    def update_all_blind_gate_predictions(self):
        """更新所有三条路的盲门预测数据"""
        self.update_blind_gate_predictions_for_road(1)
        self.update_blind_gate_predictions_for_road(2)
        self.update_blind_gate_predictions_for_road(3)

    def update_blind_gate_predictions_for_road(self, road_index: int):
        """为指定路更新盲门预测数据"""
        if road_index == 1:
            road_data = self.game_state.three_column_road_1
            blind_gate_state = self.game_state.blind_gate_state_1
            predictions_list = self.game_state.blind_gate_predictions_1
        elif road_index == 2:
            road_data = self.game_state.three_column_road_2
            blind_gate_state = self.game_state.blind_gate_state_2
            predictions_list = self.game_state.blind_gate_predictions_2
        elif road_index == 3:
            road_data = self.game_state.three_column_road_3
            blind_gate_state = self.game_state.blind_gate_state_3
            predictions_list = self.game_state.blind_gate_predictions_3
        else:
            return

        # 更新盲门状态
        self.update_blind_gate_state(road_data, blind_gate_state)

        # 如果盲门状态允许预测，重新生成预测（保留已确认的预测）
        if blind_gate_state.status in [BlindGateStatus.CONFIRMED, BlindGateStatus.TEMPORARY]:
            self.regenerate_blind_gate_predictions(road_data, blind_gate_state, predictions_list)
        else:
            # 即使状态不允许新预测，也要更新已存在预测的actual_value
            self.update_existing_predictions_actual_values(road_data, predictions_list)

    def update_blind_gate_state(self, road_data: RoadData, blind_gate_state: BlindGateState):
        """更新盲门状态"""
        # 重新计算分类序列
        blind_gate_state.observed_classifications.clear()
        blind_gate_state.unique_classifications.clear()

        # 只处理完整的3口列
        complete_columns = [col for col in road_data.columns if len(col) == 3]

        for column in complete_columns:
            classification = self.classify_column_for_blind_gate(column)
            blind_gate_state.add_classification(classification)

        # 根据分类数量和组合确定盲门状态
        classification_count = blind_gate_state.get_classification_count()

        # 记录盲门确定前的状态
        previous_status = blind_gate_state.status

        # 修改：前三列不能确定盲门，必须要有4列
        if classification_count >= 4:
            # 优先检查是否有{1,2,3,4}这4种不同分类，如果有则停止预测
            unique_classifications = blind_gate_state.unique_classifications
            if unique_classifications == {1, 2, 3, 4}:
                blind_gate_state.status = BlindGateStatus.STOPPED
                blind_gate_state.blind_gate_number = None
                blind_gate_state.direction = None
            else:
                # 先尝试规则A（基于前3个分类）
                if blind_gate_state.status == BlindGateStatus.WAITING:
                    self.determine_blind_gate_by_rule_a(blind_gate_state)
                # 如果规则A没有确定盲门，再尝试规则B
                if blind_gate_state.status == BlindGateStatus.WAITING:
                    self.determine_blind_gate_by_rule_b(blind_gate_state)
        # 注意：classification_count < 4 时不做任何处理，保持WAITING状态

        # 检查盲门是否是刚刚确定的
        just_determined = (previous_status == BlindGateStatus.WAITING and
                          blind_gate_state.status in [BlindGateStatus.CONFIRMED, BlindGateStatus.TEMPORARY])

        # 检查是否需要转为确定盲门或暂停/恢复（基于最后一个完整分类）
        if (blind_gate_state.status in [BlindGateStatus.CONFIRMED, BlindGateStatus.TEMPORARY, BlindGateStatus.PAUSED] and
            blind_gate_state.blind_gate_number and
            classification_count > 0):

            # 首先检查是否应该转为确定盲门（有3种或以上不同分类）
            if (blind_gate_state.status == BlindGateStatus.TEMPORARY and
                blind_gate_state.get_unique_count() >= 3):
                # 重新确定盲门数字（基于3种分类的组合）
                self.recalculate_blind_gate_number_for_confirmed(blind_gate_state)
                # 注意：recalculate_blind_gate_number_for_confirmed方法会设置正确的状态
                # 对于{1,3,4}组合会设置为STOPPED，其他组合会保持当前状态
                if blind_gate_state.status != BlindGateStatus.STOPPED:
                    blind_gate_state.status = BlindGateStatus.CONFIRMED

            # 然后检查是否需要暂停或恢复（但刚刚确定的盲门不会被立即暂停）
            if not just_determined:
                # 只有当刚刚完成一列时，才检查暂停条件
                newly_completed_classification = self.get_newly_completed_classification(road_data)
                if newly_completed_classification is not None:
                    if newly_completed_classification == blind_gate_state.blind_gate_number:
                        # 遇到盲门分类，暂停预测
                        blind_gate_state.status = BlindGateStatus.PAUSED
                    elif blind_gate_state.status == BlindGateStatus.PAUSED:
                        # 遇到非盲门分类，恢复预测
                        if blind_gate_state.get_unique_count() >= 3:
                            blind_gate_state.status = BlindGateStatus.CONFIRMED
                        else:
                            blind_gate_state.status = BlindGateStatus.TEMPORARY

    def recalculate_blind_gate_number_for_confirmed(self, blind_gate_state: BlindGateState):
        """当转为确定盲门时，重新计算盲门数字（基于3种分类的组合）"""
        all_classifications = blind_gate_state.observed_classifications
        unique_set = set(all_classifications)

        # 根据3种分类的组合确定盲门数字
        if unique_set == {1, 2, 3}:
            # 1,2,3组合 → 盲门4，打连
            blind_gate_state.blind_gate_number = 4
            blind_gate_state.direction = BlindGateDirection.LIAN
        elif unique_set == {1, 2, 4}:
            # 1,2,4组合 → 盲门3，打正
            blind_gate_state.blind_gate_number = 3
            blind_gate_state.direction = BlindGateDirection.ZHENG
        elif unique_set == {2, 3, 4}:
            # 2,3,4组合 → 盲门1，打反
            blind_gate_state.blind_gate_number = 1
            blind_gate_state.direction = BlindGateDirection.FAN
        elif unique_set == {1, 3, 4}:
            # 1,3,4组合 → 无盲门，停止预测
            blind_gate_state.status = BlindGateStatus.STOPPED
            blind_gate_state.blind_gate_number = None
            blind_gate_state.direction = None

    def get_newly_completed_classification(self, road_data: RoadData) -> int:
        """获取刚刚完成的列的分类（如果有的话）"""
        if road_data.columns:
            last_column = road_data.columns[-1]
            # 只有当最后一列刚好完成（长度为3）时，才返回其分类
            if len(last_column) == 3:
                return self.classify_column_for_blind_gate(last_column)
        return None

    def classify_column_for_blind_gate(self, column: List[GameResult]) -> int:
        """为盲门预测对列进行1-4分类"""
        if len(column) != 3:
            return 0  # 无效分类

        # 转换为字符串便于比较
        col_str = [r.value for r in column]

        # 根据模式进行分类
        if col_str[0] == col_str[1] == col_str[2]:  # BBB 或 PPP
            return 1
        elif col_str[0] == col_str[1] and col_str[0] != col_str[2]:  # BBP 或 PPB
            return 2
        elif col_str[1] == col_str[2] and col_str[0] != col_str[1]:  # BPP 或 PBB
            return 3
        elif col_str[0] == col_str[2] and col_str[0] != col_str[1]:  # BPB 或 PBP
            return 4
        else:
            return 0  # 无效分类

    def determine_blind_gate_by_rule_a(self, blind_gate_state: BlindGateState):
        """根据规则A确定盲门（3株情况）"""
        if blind_gate_state.get_classification_count() < 3:
            return

        # 取前3个分类
        first_three = blind_gate_state.observed_classifications[:3]
        unique_three = set(first_three)

        if unique_three == {1, 2, 3}:
            # 1,2,3组合 → 盲门4，打连
            blind_gate_state.blind_gate_number = 4
            blind_gate_state.direction = BlindGateDirection.LIAN
            blind_gate_state.status = BlindGateStatus.CONFIRMED
        elif unique_three == {1, 2, 4}:
            # 1,2,4组合 → 盲门3，打正
            blind_gate_state.blind_gate_number = 3
            blind_gate_state.direction = BlindGateDirection.ZHENG
            blind_gate_state.status = BlindGateStatus.CONFIRMED
        elif unique_three == {2, 3, 4}:
            # 2,3,4组合 → 盲门1，打反
            blind_gate_state.blind_gate_number = 1
            blind_gate_state.direction = BlindGateDirection.FAN
            blind_gate_state.status = BlindGateStatus.CONFIRMED
        elif unique_three == {1, 3, 4}:
            # 1,3,4组合 → 无盲门，停止预测
            blind_gate_state.status = BlindGateStatus.STOPPED

    def determine_blind_gate_by_rule_b(self, blind_gate_state: BlindGateState):
        """根据规则B确定盲门（4株情况）"""
        if blind_gate_state.get_classification_count() < 4:
            return

        # 取前4个分类
        first_four = blind_gate_state.observed_classifications[:4]
        unique_count = len(set(first_four))

        # 如果包含3种数字，重新用规则A确定盲门
        if unique_count == 3:
            unique_set = set(first_four)
            if unique_set == {1, 2, 3}:
                blind_gate_state.blind_gate_number = 4
                blind_gate_state.direction = BlindGateDirection.LIAN
                blind_gate_state.status = BlindGateStatus.CONFIRMED
            elif unique_set == {1, 2, 4}:
                blind_gate_state.blind_gate_number = 3
                blind_gate_state.direction = BlindGateDirection.ZHENG
                blind_gate_state.status = BlindGateStatus.CONFIRMED
            elif unique_set == {2, 3, 4}:
                blind_gate_state.blind_gate_number = 1
                blind_gate_state.direction = BlindGateDirection.FAN
                blind_gate_state.status = BlindGateStatus.CONFIRMED
            elif unique_set == {1, 3, 4}:
                blind_gate_state.status = BlindGateStatus.STOPPED
            return

        # 如果包含1-2种数字，使用规则B的组合
        first_four_tuple = tuple(first_four)

        # 规则B的组合定义（标准模式）
        blind_gate_4_combinations = [
            (1,1,1,1), (2,2,2,2), (1,1,2,2), (1,1,1,2), (1,1,3,3), (1,1,1,3), (1,1,2,3),
            (2,2,1,3), (3,3,1,2), (2,2,3,3), (2,2,2,1), (2,2,2,3), (3,3,3,1), (3,3,3,2)
        ]

        blind_gate_1_combinations = [
            (1,1,4,4), (1,1,1,4), (1,1,2,4), (2,2,1,4), (4,4,1,2), (2,2,4,4), (4,4,4,1)
        ]

        blind_gate_3_combinations = [
            (3,3,3,3), (4,4,4,4), (4,4,2,3), (3,3,4,4), (2,2,2,4), (2,2,3,4),
            (3,3,2,4), (3,3,3,4), (4,4,4,2), (4,4,4,3)
        ]

        # 使用数字组成匹配而不是严格顺序匹配
        if self.matches_combination_pattern(first_four_tuple, blind_gate_4_combinations):
            blind_gate_state.blind_gate_number = 4
            blind_gate_state.direction = BlindGateDirection.LIAN
            blind_gate_state.status = BlindGateStatus.TEMPORARY
        elif self.matches_combination_pattern(first_four_tuple, blind_gate_1_combinations):
            blind_gate_state.blind_gate_number = 1
            blind_gate_state.direction = BlindGateDirection.ZHENG
            blind_gate_state.status = BlindGateStatus.TEMPORARY
        elif self.matches_combination_pattern(first_four_tuple, blind_gate_3_combinations):
            blind_gate_state.blind_gate_number = 3
            blind_gate_state.direction = BlindGateDirection.FAN
            blind_gate_state.status = BlindGateStatus.TEMPORARY

    def matches_combination_pattern(self, target_tuple: tuple, pattern_list: list) -> bool:
        """检查目标元组是否匹配模式列表中的任一模式（基于数字组成而非顺序）"""
        from collections import Counter

        # 计算目标元组的数字组成
        target_counter = Counter(target_tuple)

        # 检查是否与任一模式匹配
        for pattern in pattern_list:
            pattern_counter = Counter(pattern)
            if target_counter == pattern_counter:
                return True

        return False

    def generate_blind_gate_predictions(self, road_data: RoadData, blind_gate_state: BlindGateState,
                                      predictions_list: List[BlindGateColumnPrediction]):
        """生成盲门预测"""
        if not blind_gate_state.direction:
            return

        # 确定从哪一列开始预测（跳过用于确定盲门的前几列）
        # 重要：只对未来的列进行预测，不对已完成的列进行回顾性预测
        if blind_gate_state.get_classification_count() >= 3:
            min_start_col_idx = 3  # 最早从第4列开始预测
        else:
            min_start_col_idx = 4  # 最早从第5列开始预测

        # 实际起始列应该是：max(最小起始列, 已完成列数)
        complete_columns_count = len([col for col in road_data.columns if len(col) == 3])
        start_col_idx = max(min_start_col_idx, complete_columns_count)

        # 为所有符合条件的列生成预测（只预测正在进行或未来的列）
        for col_idx in range(start_col_idx, road_data.get_column_count()):
            current_col = road_data.columns[col_idx]

            # 检查是否已经存在该列的预测
            existing_prediction = None
            for pred_info in predictions_list:
                if pred_info.column_index == col_idx:
                    existing_prediction = pred_info
                    break

            # 如果已经存在预测，检查是否需要添加新的预测（如第3口预测）
            if existing_prediction:
                # 检查是否需要添加第3口预测
                has_3rd_prediction = any(pred.position == 3 for pred in existing_prediction.predictions)

                # 如果第2口预测错误且没有第3口预测，则添加第3口预测
                if (len(current_col) >= 2 and
                    existing_prediction.predictions and
                    not has_3rd_prediction):

                    # 找到第2口预测
                    second_pred = None
                    for pred in existing_prediction.predictions:
                        if pred.position == 2:
                            second_pred = pred
                            break

                    if (second_pred and
                        second_pred.actual_value is not None and
                        second_pred.predicted_value != second_pred.actual_value):

                        # 第2口预测错误，添加第3口预测
                        predicted_3rd = self.calculate_blind_gate_prediction(
                            current_col, 3, blind_gate_state.direction
                        )
                        if predicted_3rd:
                            existing_prediction.predictions.append(BlindGatePrediction(
                                position=3,
                                predicted_value=predicted_3rd,
                                direction=blind_gate_state.direction,
                                actual_value=current_col[2] if len(current_col) >= 3 else None
                            ))

                continue  # 跳过已存在预测的列

            column_predictions = []

            # 预测第2口
            if len(current_col) >= 1:
                predicted_2nd = self.calculate_blind_gate_prediction(
                    current_col, 2, blind_gate_state.direction
                )
                if predicted_2nd:
                    column_predictions.append(BlindGatePrediction(
                        position=2,
                        predicted_value=predicted_2nd,
                        actual_value=current_col[1] if len(current_col) >= 2 else None,
                        direction=blind_gate_state.direction
                    ))

            # 预测第3口（只有第2口预测错了才预测第3口）
            if len(current_col) >= 2 and column_predictions:
                second_prediction_correct = (column_predictions[0].predicted_value == current_col[1])

                if not second_prediction_correct:
                    predicted_3rd = self.calculate_blind_gate_prediction(
                        current_col, 3, blind_gate_state.direction
                    )
                    if predicted_3rd:
                        column_predictions.append(BlindGatePrediction(
                            position=3,
                            predicted_value=predicted_3rd,
                            actual_value=current_col[2] if len(current_col) >= 3 else None,
                            direction=blind_gate_state.direction
                        ))

            if column_predictions:
                classification = self.classify_column_for_blind_gate(current_col) if len(current_col) == 3 else 0
                predictions_list.append(BlindGateColumnPrediction(
                    column_index=col_idx,
                    column_classification=classification,
                    direction=blind_gate_state.direction,
                    predictions=column_predictions
                ))

    def calculate_blind_gate_prediction(self, current_col: List[GameResult], position: int,
                                      direction: BlindGateDirection) -> Optional[GameResult]:
        """计算盲门预测值"""
        if direction == BlindGateDirection.LIAN:
            # 打连：预测值为前一口的值
            if position == 2 and len(current_col) >= 1:
                return current_col[0]  # 第2口跟第1口
            elif position == 3 and len(current_col) >= 2:
                return current_col[1]  # 第3口跟第2口

        elif direction == BlindGateDirection.ZHENG:
            # 打正：预测值为当前株第一口的值
            if len(current_col) >= 1:
                return current_col[0]

        elif direction == BlindGateDirection.FAN:
            # 打反：预测值为当前株第一口的相反值
            if len(current_col) >= 1:
                return GameResult.PLAYER if current_col[0] == GameResult.BANKER else GameResult.BANKER

        return None

    def update_blind_gate_positive_negative_road_for_predictions(self,
                                                               predictions: List[BlindGateColumnPrediction]) -> List[str]:
        """为盲门预测数据更新正负路数据"""
        positive_negative_road = []

        for pred_info in predictions:
            for pred in pred_info.predictions:
                if pred.actual_value is not None:  # 只记录已确认的预测
                    if pred.predicted_value == pred.actual_value:
                        positive_negative_road.append('+')  # 预测正确
                    else:
                        positive_negative_road.append('-')  # 预测错误

        return positive_negative_road

    def get_next_blind_gate_prediction_for_road(self, road_index: int) -> Optional[dict]:
        """获取指定路的下一个盲门预测信息"""
        if road_index == 1:
            predictions = self.game_state.blind_gate_predictions_1
            blind_gate_state = self.game_state.blind_gate_state_1
        elif road_index == 2:
            predictions = self.game_state.blind_gate_predictions_2
            blind_gate_state = self.game_state.blind_gate_state_2
        elif road_index == 3:
            predictions = self.game_state.blind_gate_predictions_3
            blind_gate_state = self.game_state.blind_gate_state_3
        else:
            return None

        # 如果状态不允许预测，返回None
        if blind_gate_state.status not in [BlindGateStatus.CONFIRMED, BlindGateStatus.TEMPORARY, BlindGateStatus.PAUSED]:
            return None

        # 如果是暂停状态，需要检查当前是否还在盲门分类的列中
        if blind_gate_state.status == BlindGateStatus.PAUSED:
            # 获取当前正在进行的列
            if road_index == 1:
                road_data = self.game_state.three_column_road_1
            elif road_index == 2:
                road_data = self.game_state.three_column_road_2
            else:
                road_data = self.game_state.three_column_road_3

            # 检查最后一列是否还是盲门分类
            if road_data.columns:
                last_col = road_data.columns[-1]
                if len(last_col) == 3:
                    classification = self.classify_column_for_blind_gate(last_col)
                    if classification == blind_gate_state.blind_gate_number:
                        return None  # 还在盲门分类中，暂停预测

        for pred_info in predictions:
            for pred in pred_info.predictions:
                if pred.actual_value is None:  # 找到第一个未确认的预测
                    return {
                        'value': pred.predicted_value,
                        'direction': pred.direction,
                        'blind_gate': blind_gate_state.blind_gate_number,
                        'status': blind_gate_state.status
                    }
        return None

    def calculate_blind_gate_accuracy_for_road(self, road_index: int) -> dict:
        """计算指定路的盲门预测准确率"""
        if road_index == 1:
            predictions = self.game_state.blind_gate_predictions_1
        elif road_index == 2:
            predictions = self.game_state.blind_gate_predictions_2
        elif road_index == 3:
            predictions = self.game_state.blind_gate_predictions_3
        else:
            return {'correct': 0, 'total': 0, 'percentage': 0.0}

        total = correct = 0

        for pred_info in predictions:
            for pred in pred_info.predictions:
                if pred.actual_value is not None:
                    total += 1
                    if pred.predicted_value == pred.actual_value:
                        correct += 1

        percentage = (correct / total * 100) if total > 0 else 0.0
        return {'correct': correct, 'total': total, 'percentage': percentage}

    def get_blind_gate_status_text(self, road_index: int) -> str:
        """获取盲门状态文本"""
        if road_index == 1:
            blind_gate_state = self.game_state.blind_gate_state_1
        elif road_index == 2:
            blind_gate_state = self.game_state.blind_gate_state_2
        elif road_index == 3:
            blind_gate_state = self.game_state.blind_gate_state_3
        else:
            return "无效路"

        status_map = {
            BlindGateStatus.WAITING: "等待中",
            BlindGateStatus.TEMPORARY: f"临时盲门{blind_gate_state.blind_gate_number}",
            BlindGateStatus.CONFIRMED: f"确定盲门{blind_gate_state.blind_gate_number}",
            BlindGateStatus.PAUSED: f"暂停(盲门{blind_gate_state.blind_gate_number})",
            BlindGateStatus.STOPPED: "停止预测"
        }

        return status_map.get(blind_gate_state.status, "未知状态")

    def get_blind_gate_direction_text(self, road_index: int) -> str:
        """获取盲门预测方向文本"""
        if road_index == 1:
            blind_gate_state = self.game_state.blind_gate_state_1
        elif road_index == 2:
            blind_gate_state = self.game_state.blind_gate_state_2
        elif road_index == 3:
            blind_gate_state = self.game_state.blind_gate_state_3
        else:
            return ""

        if not blind_gate_state.direction:
            return ""

        direction_map = {
            BlindGateDirection.LIAN: "打连",
            BlindGateDirection.ZHENG: "打正",
            BlindGateDirection.FAN: "打反"
        }

        return direction_map.get(blind_gate_state.direction, "")

    def update_existing_predictions_actual_values(self, road_data: RoadData,
                                                 predictions_list: List[BlindGateColumnPrediction]):
        """更新已存在预测的actual_value"""
        for pred_info in predictions_list:
            col_idx = pred_info.column_index
            if col_idx < len(road_data.columns):
                current_col = road_data.columns[col_idx]

                for pred in pred_info.predictions:
                    if pred.position == 2 and len(current_col) >= 2:
                        # 更新第2口预测的actual_value
                        pred.actual_value = current_col[1]
                    elif pred.position == 3 and len(current_col) >= 3:
                        # 更新第3口预测的actual_value
                        pred.actual_value = current_col[2]

    def regenerate_blind_gate_predictions(self, road_data: RoadData, blind_gate_state: BlindGateState,
                                        predictions_list: List[BlindGateColumnPrediction]):
        """重新生成盲门预测（保留已确认的预测）"""
        if not blind_gate_state.direction:
            return

        # 保留所有现有预测（不仅仅是已确认的）
        existing_predictions = []
        for pred_info in predictions_list:
            if pred_info.predictions:
                # 创建新的预测信息，包含所有预测
                existing_pred_info = BlindGateColumnPrediction(
                    column_index=pred_info.column_index,
                    column_classification=pred_info.column_classification,
                    direction=pred_info.direction,
                    predictions=pred_info.predictions.copy()  # 复制所有预测
                )
                existing_predictions.append(existing_pred_info)

        # 清空预测列表并添加现有预测
        predictions_list.clear()
        predictions_list.extend(existing_predictions)

        # 先更新已存在预测的actual_value
        self.update_existing_predictions_actual_values(road_data, predictions_list)

        # 再生成新的预测（从当前位置开始）
        self.generate_blind_gate_predictions(road_data, blind_gate_state, predictions_list)
