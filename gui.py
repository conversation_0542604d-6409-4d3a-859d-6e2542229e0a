"""
百家乐分析器GUI界面模块
专注于界面布局、事件处理、显示更新，移除业务逻辑
"""

import tkinter as tk
from tkinter import ttk
from typing import List
from models import GameResult, HistoryState
from analyzer import BaccaratAnalyzer
from profit_calculator import ProfitCalculator
from drawing import CanvasFactory, RoadDrawer


class BaccaratGUI:
    """百家乐分析器GUI界面"""
    
    def __init__(self, root):
        self.root = root
        self.root.title("百家乐分析器")
        self.root.geometry("1000x800")
        self.root.resizable(False, False)
        
        # 业务逻辑组件
        self.analyzer = BaccaratAnalyzer()
        self.profit_calculator = ProfitCalculator()
        
        # 历史记录用于撤回
        self.history: List[HistoryState] = []
        
        # UI组件
        self.canvas_widgets = {}
        self.info_labels = {}
        
        self.setup_ui()
    
    def setup_ui(self):
        """设置用户界面"""
        main_frame = ttk.Frame(self.root, padding="5")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # 路图显示区域
        self.create_road_displays(main_frame)
        
        # 操作按钮
        self.create_buttons(main_frame)
    
    def create_road_displays(self, parent):
        """创建路图显示区域"""
        # 上面一行：珠盘路和大路并列
        top_frame = ttk.Frame(parent)
        top_frame.grid(row=0, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 5))
        
        # 珠盘路（上面左侧）
        self._create_bead_road_section(top_frame)
        
        # 大路图（上面右侧）
        self._create_big_road_section(top_frame)
        
        # 配置上面一行的网格权重
        top_frame.grid_columnconfigure(0, weight=1)
        top_frame.grid_columnconfigure(1, weight=1)
        
        # 下面一行：三条三珠路并列
        bottom_frame = ttk.Frame(parent)
        bottom_frame.grid(row=1, column=0, columnspan=2, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # 三条三珠路
        self._create_three_column_roads_section(bottom_frame)
        
        # 配置下面一行的网格权重
        bottom_frame.grid_columnconfigure(0, weight=1)
        bottom_frame.grid_columnconfigure(1, weight=1)
        bottom_frame.grid_columnconfigure(2, weight=1)
    
    def _create_bead_road_section(self, parent):
        """创建珠盘路区域"""
        bead_frame = ttk.LabelFrame(parent, text="珠盘路", padding="3")
        bead_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S), padx=(0, 3))
        
        # 计算画布尺寸
        bead_canvas_height = 6 * 20 + 20
        bead_canvas_width = 11 * 20 + 20
        
        self.canvas_widgets['bead'] = CanvasFactory.create_bead_road_canvas(
            bead_frame, width=bead_canvas_width, height=bead_canvas_height
        )
    
    def _create_big_road_section(self, parent):
        """创建大路图区域"""
        big_road_frame = ttk.LabelFrame(parent, text="大路图", padding="3")
        big_road_frame.grid(row=0, column=1, sticky=(tk.W, tk.E, tk.N, tk.S), padx=(3, 0))
        
        # 计算画布尺寸
        canvas_height = 6 * 20 + 20
        canvas_width = 680
        
        self.canvas_widgets['big_road'] = CanvasFactory.create_big_road_canvas(
            big_road_frame, width=canvas_width, height=canvas_height
        )
    
    def _create_three_column_roads_section(self, parent):
        """创建三条三珠路区域"""
        for i in range(1, 4):
            self._create_single_three_column_road(parent, i)
    
    def _create_single_three_column_road(self, parent, road_index: int):
        """创建单条三珠路"""
        # 计算列位置
        col_position = road_index - 1
        padx = (0, 2) if road_index == 1 else (2, 2) if road_index == 2 else (2, 0)

        # 创建主框架
        main_frame = ttk.Frame(parent)
        main_frame.grid(row=0, column=col_position, sticky=(tk.W, tk.E, tk.N, tk.S), padx=padx)

        # 1. 三株路显示部分
        road_frame = ttk.LabelFrame(main_frame, text=f"{road_index}路", padding="3")
        road_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S), pady=(0, 5))

        # 创建画布
        canvas_height = 45 + 3 * 15 + 25 + 8
        canvas_width = 18 * 15 + 20

        canvas_key = f'three_col_{road_index}'
        self.canvas_widgets[canvas_key] = CanvasFactory.create_three_column_canvas(
            road_frame, width=canvas_width, height=canvas_height
        )

        # 2. 连跳预测信息部分
        self._create_jump_prediction_section(main_frame, road_index)

        # 3. 盲门预测信息部分
        self._create_blind_gate_section(main_frame, road_index)
    
    def _create_jump_prediction_section(self, parent, road_index: int):
        """创建连跳预测信息部分"""
        jump_frame = ttk.LabelFrame(parent, text="连跳预测", padding="3")
        jump_frame.grid(row=1, column=0, sticky=(tk.W, tk.E, tk.N, tk.S), pady=(0, 5))

        # 创建连跳预测相关标签
        labels = {}

        # 连跳预测信息
        labels['prediction'] = ttk.Label(jump_frame, text="连跳预测：无", font=("Arial", 18, "bold"))
        labels['prediction'].grid(row=0, column=0, sticky=tk.W)

        # 下注金额（普通连跳）
        labels['bet'] = ttk.Label(jump_frame, text="连跳下注：20", font=("Arial", 9))
        labels['bet'].grid(row=1, column=0, sticky=tk.W)

        # 胜率（普通连跳）
        labels['accuracy'] = ttk.Label(jump_frame, text="连跳胜率：0%", font=("Arial", 9))
        labels['accuracy'].grid(row=2, column=0, sticky=tk.W)

        # 收益（普通连跳）
        labels['profit'] = ttk.Label(jump_frame, text="连跳收益：0", font=("Arial", 9))
        labels['profit'].grid(row=3, column=0, sticky=tk.W)

        # 正负路（普通连跳）
        labels['pos_neg'] = ttk.Label(jump_frame, text="连跳正负路：", font=("Courier", 7),
                                     wraplength=280, justify='left')
        labels['pos_neg'].grid(row=4, column=0, sticky=(tk.W, tk.N))

        # 行缆（普通连跳）
        labels['ladder_cable'] = ttk.Label(jump_frame, text="连跳行缆：", font=("Courier", 7),
                                          wraplength=280, justify='left')
        labels['ladder_cable'].grid(row=5, column=0, sticky=(tk.W, tk.N))

        # 保存标签引用（如果还没有创建字典）
        if f'road_{road_index}' not in self.info_labels:
            self.info_labels[f'road_{road_index}'] = {}
        self.info_labels[f'road_{road_index}'].update(labels)

    def _create_blind_gate_section(self, parent, road_index: int):
        """创建盲门预测信息部分"""
        blind_gate_frame = ttk.LabelFrame(parent, text="盲门预测", padding="3")
        blind_gate_frame.grid(row=2, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))

        # 创建盲门预测相关标签
        labels = {}

        # 盲门预测信息
        labels['blind_gate_prediction'] = ttk.Label(blind_gate_frame, text="盲门预测：无", font=("Arial", 18, "bold"))
        labels['blind_gate_prediction'].grid(row=0, column=0, sticky=tk.W)

        # 盲门状态
        labels['blind_gate_status'] = ttk.Label(blind_gate_frame, text="盲门状态：等待中", font=("Arial", 9))
        labels['blind_gate_status'].grid(row=1, column=0, sticky=tk.W)

        # 盲门方向
        labels['blind_gate_direction'] = ttk.Label(blind_gate_frame, text="预测方向：", font=("Arial", 9))
        labels['blind_gate_direction'].grid(row=2, column=0, sticky=tk.W)

        # 下注金额（盲门）
        labels['blind_gate_bet'] = ttk.Label(blind_gate_frame, text="盲门下注：20", font=("Arial", 9))
        labels['blind_gate_bet'].grid(row=3, column=0, sticky=tk.W)

        # 胜率（盲门）
        labels['blind_gate_accuracy'] = ttk.Label(blind_gate_frame, text="盲门胜率：0%", font=("Arial", 9))
        labels['blind_gate_accuracy'].grid(row=4, column=0, sticky=tk.W)

        # 收益（盲门）
        labels['blind_gate_profit'] = ttk.Label(blind_gate_frame, text="盲门收益：0", font=("Arial", 9))
        labels['blind_gate_profit'].grid(row=5, column=0, sticky=tk.W)

        # 正负路（盲门）
        labels['blind_gate_pos_neg'] = ttk.Label(blind_gate_frame, text="盲门正负路：", font=("Courier", 7),
                                               wraplength=280, justify='left')
        labels['blind_gate_pos_neg'].grid(row=6, column=0, sticky=(tk.W, tk.N))

        # 行缆（盲门）
        labels['blind_gate_ladder_cable'] = ttk.Label(blind_gate_frame, text="盲门行缆：", font=("Courier", 7),
                                                    wraplength=280, justify='left')
        labels['blind_gate_ladder_cable'].grid(row=7, column=0, sticky=(tk.W, tk.N))

        # 更新标签引用
        self.info_labels[f'road_{road_index}'].update(labels)
    
    def create_buttons(self, parent):
        """创建操作按钮"""
        button_frame = ttk.Frame(parent)
        button_frame.grid(row=2, column=0, columnspan=2, pady=(10, 0), sticky=(tk.W, tk.E))
        
        # 楼梯缆信息显示
        ladder_info_text = self.profit_calculator.get_ladder_info_text()
        ladder_info_label = ttk.Label(button_frame, text=ladder_info_text, font=("Arial", 10))
        ladder_info_label.grid(row=0, column=0, sticky=tk.W)
        
        # 按钮容器
        buttons_container = ttk.Frame(button_frame)
        buttons_container.place(relx=0.5, rely=0.5, anchor='center')
        
        # 游戏结果按钮
        style = ttk.Style()
        style.configure('Game.TButton', font=('Arial', 12, 'bold'))
        
        ttk.Button(buttons_container, text="红", command=lambda: self.add_result(GameResult.BANKER),
                  style='Game.TButton', width=8).grid(row=0, column=0, padx=3)
        ttk.Button(buttons_container, text="蓝", command=lambda: self.add_result(GameResult.PLAYER),
                  style='Game.TButton', width=8).grid(row=0, column=1, padx=3)
        ttk.Button(buttons_container, text="和", command=lambda: self.add_result(GameResult.TIE),
                  style='Game.TButton', width=8).grid(row=0, column=2, padx=3)
        
        # 操作按钮
        ttk.Button(buttons_container, text="撤回", command=self.undo_last, width=6).grid(row=0, column=3, padx=3)
        ttk.Button(buttons_container, text="清除", command=self.clear_results, width=6).grid(row=0, column=4, padx=3)
    
    def add_result(self, result: GameResult):
        """添加庄闲和结果并更新显示"""
        # 保存当前状态用于撤回
        self.save_current_state()
        
        # 添加结果到分析器
        self.analyzer.add_result(result)
        
        # 如果是庄或闲，且有预测值，自动进行收益结算
        if result in [GameResult.BANKER, GameResult.PLAYER]:
            game_state = self.analyzer.get_game_state()
            self.profit_calculator.update_road_profits(
                result,
                game_state.predictions_1,
                game_state.predictions_2,
                game_state.predictions_3,
                game_state.blind_gate_predictions_1,
                game_state.blind_gate_predictions_2,
                game_state.blind_gate_predictions_3
            )
        
        # 刷新显示
        self.refresh_display()
    
    def save_current_state(self):
        """保存当前状态用于撤回"""
        snapshot = HistoryState.create_snapshot(
            self.analyzer.get_game_state(),
            self.profit_calculator.get_profit_state(),
            self.profit_calculator.get_ladder_state()
        )
        self.history.append(snapshot)
    
    def undo_last(self):
        """撤回上一步操作"""
        if not self.history:
            return
        
        # 恢复上一个状态
        last_state = self.history.pop()
        
        # 恢复分析器状态
        self.analyzer.game_state = last_state.game_state
        
        # 恢复收益计算器状态
        self.profit_calculator.profit_state = last_state.profit_state
        self.profit_calculator.ladder_state = last_state.ladder_state
        
        self.refresh_display()
    
    def clear_results(self):
        """清除所有数据"""
        self.analyzer.clear_all_data()
        self.profit_calculator.clear_all_profits()
        self.history.clear()
        self.refresh_display()
    
    def refresh_display(self):
        """刷新所有显示"""
        self._clear_all_canvases()
        self._draw_all_roads()
        self._update_all_info_displays()
    
    def _clear_all_canvases(self):
        """清空所有画布"""
        for canvas in self.canvas_widgets.values():
            canvas.delete("all")
    
    def _draw_all_roads(self):
        """绘制所有路图"""
        game_state = self.analyzer.get_game_state()
        
        # 绘制珠盘路
        RoadDrawer.draw_bead_road(self.canvas_widgets['bead'], game_state.results)
        
        # 绘制大路图
        RoadDrawer.draw_big_road(self.canvas_widgets['big_road'], game_state.big_road)
        
        # 绘制三条三珠路
        roads = [game_state.three_column_road_1, game_state.three_column_road_2, game_state.three_column_road_3]
        for i, road in enumerate(roads, 1):
            canvas_key = f'three_col_{i}'
            RoadDrawer.draw_three_column_road(self.canvas_widgets[canvas_key], road)

    def _update_all_info_displays(self):
        """更新所有信息显示"""
        for i in range(1, 4):
            self._update_road_info_display(i)

    def _update_road_info_display(self, road_index: int):
        """更新指定路的信息显示"""
        labels = self.info_labels[f'road_{road_index}']
        game_state = self.analyzer.get_game_state()

        # 更新普通连跳预测信息
        prediction_info = self.analyzer.get_next_prediction_for_road(road_index)
        if prediction_info:
            predicted_text = '红' if prediction_info['value'] == GameResult.BANKER else '蓝'
            predicted_color = 'red' if prediction_info['value'] == GameResult.BANKER else 'blue'
            labels['prediction'].config(text=f"连跳预测：{predicted_text}", foreground=predicted_color)
        else:
            labels['prediction'].config(text="连跳预测：无", foreground='black')

        # 更新盲门预测信息
        blind_gate_prediction_info = self.analyzer.get_next_blind_gate_prediction_for_road(road_index)
        if blind_gate_prediction_info:
            predicted_text = '红' if blind_gate_prediction_info['value'] == GameResult.BANKER else '蓝'
            predicted_color = 'red' if blind_gate_prediction_info['value'] == GameResult.BANKER else 'blue'
            labels['blind_gate_prediction'].config(text=f"盲门预测：{predicted_text}", foreground=predicted_color)
        else:
            labels['blind_gate_prediction'].config(text="盲门预测：无", foreground='black')

        # 更新盲门状态
        blind_gate_status_text = self.analyzer.get_blind_gate_status_text(road_index)
        labels['blind_gate_status'].config(text=f"盲门状态：{blind_gate_status_text}")

        # 更新盲门方向
        blind_gate_direction_text = self.analyzer.get_blind_gate_direction_text(road_index)
        labels['blind_gate_direction'].config(text=f"预测方向：{blind_gate_direction_text}")

        # 更新普通连跳胜率
        accuracy = self.analyzer.calculate_accuracy_for_road(road_index)
        accuracy_text = self.profit_calculator.format_accuracy_text(
            accuracy['correct'], accuracy['total']
        )
        labels['accuracy'].config(text=f"连跳胜率：{accuracy_text}")

        # 更新盲门胜率
        blind_gate_accuracy = self.analyzer.calculate_blind_gate_accuracy_for_road(road_index)
        blind_gate_accuracy_text = self.profit_calculator.format_accuracy_text(
            blind_gate_accuracy['correct'], blind_gate_accuracy['total']
        )
        labels['blind_gate_accuracy'].config(text=f"盲门胜率：{blind_gate_accuracy_text}")

        # 获取普通连跳正负路（一次性获取，避免重复）
        pos_neg_road = self._get_positive_negative_road_for_index(road_index, game_state)

        # 更新普通连跳下注金额（基于正负路计算）
        bet_amount = self.profit_calculator.get_bet_amount_from_pos_neg_road(pos_neg_road)
        labels['bet'].config(text=f"连跳下注：{bet_amount}")

        # 更新普通连跳收益（基于正负路计算，与行缆结果一致）
        profit = self.profit_calculator.get_profit_from_pos_neg_road(pos_neg_road)
        profit_text, profit_color = self.profit_calculator.format_profit_text(profit)
        labels['profit'].config(text=f"连跳收益：{profit_text}", foreground=profit_color)

        # 更新普通连跳正负路显示
        if pos_neg_road:
            pos_neg_text = " ".join(pos_neg_road)
            labels['pos_neg'].config(text=f"连跳正负路：{pos_neg_text}")
        else:
            labels['pos_neg'].config(text="连跳正负路：暂无数据")

        # 获取盲门正负路（一次性获取，避免重复）
        blind_gate_pos_neg_road = self._get_blind_gate_positive_negative_road_for_index(road_index, game_state)

        # 更新盲门下注金额（基于正负路计算）
        blind_gate_bet_amount = self.profit_calculator.get_blind_gate_bet_amount_from_pos_neg_road(blind_gate_pos_neg_road)
        labels['blind_gate_bet'].config(text=f"盲门下注：{blind_gate_bet_amount}")

        # 更新盲门收益（基于行缆计算）
        blind_gate_profit = self.profit_calculator.get_blind_gate_profit_for_road(road_index, blind_gate_pos_neg_road)
        blind_gate_profit_text, blind_gate_profit_color = self.profit_calculator.format_profit_text(blind_gate_profit)
        labels['blind_gate_profit'].config(text=f"盲门收益：{blind_gate_profit_text}", foreground=blind_gate_profit_color)
        if blind_gate_pos_neg_road:
            blind_gate_pos_neg_text = " ".join(blind_gate_pos_neg_road)
            labels['blind_gate_pos_neg'].config(text=f"盲门正负路：{blind_gate_pos_neg_text}")
        else:
            labels['blind_gate_pos_neg'].config(text="盲门正负路：暂无数据")

        # 更新普通连跳行缆
        if pos_neg_road:
            ladder_cable = self.profit_calculator.generate_ladder_cable_sequence(pos_neg_road)
            if ladder_cable:
                cable_text = " ".join(ladder_cable)
                labels['ladder_cable'].config(text=f"连跳行缆：{cable_text}")
            else:
                labels['ladder_cable'].config(text="连跳行缆：暂无数据")
        else:
            labels['ladder_cable'].config(text="连跳行缆：暂无数据")

        # 更新盲门行缆
        if blind_gate_pos_neg_road:
            blind_gate_ladder_cable = self.profit_calculator.generate_blind_gate_ladder_cable_sequence(blind_gate_pos_neg_road)
            if blind_gate_ladder_cable:
                blind_gate_cable_text = " ".join(blind_gate_ladder_cable)
                labels['blind_gate_ladder_cable'].config(text=f"盲门行缆：{blind_gate_cable_text}")
            else:
                labels['blind_gate_ladder_cable'].config(text="盲门行缆：暂无数据")
        else:
            labels['blind_gate_ladder_cable'].config(text="盲门行缆：暂无数据")

    def _get_positive_negative_road_for_index(self, road_index: int, game_state) -> List[str]:
        """根据路索引获取普通连跳正负路数据"""
        if road_index == 1:
            return game_state.positive_negative_road_1
        elif road_index == 2:
            return game_state.positive_negative_road_2
        elif road_index == 3:
            return game_state.positive_negative_road_3
        else:
            return []

    def _get_blind_gate_positive_negative_road_for_index(self, road_index: int, game_state) -> List[str]:
        """根据路索引获取盲门正负路数据"""
        if road_index == 1:
            return game_state.blind_gate_positive_negative_road_1
        elif road_index == 2:
            return game_state.blind_gate_positive_negative_road_2
        elif road_index == 3:
            return game_state.blind_gate_positive_negative_road_3
        else:
            return []
