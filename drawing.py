"""
百家乐分析器绘图模块
实现路图绘制逻辑、画布管理、视觉效果
"""

import tkinter as tk
from tkinter import ttk
from typing import List, Tuple
from models import GameResult, RoadData, PatternInfo


class CanvasManager:
    """画布管理器"""
    
    @staticmethod
    def create_scrollable_canvas(parent, width: int, height: int, 
                                horizontal_scroll: bool = True, 
                                vertical_scroll: bool = True) -> tk.Canvas:
        """创建可滚动的画布"""
        canvas_frame = ttk.Frame(parent)
        canvas_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        canvas = tk.Canvas(canvas_frame, width=width, height=height, 
                          bg='white', relief='solid', bd=1)
        
        # 滚动条
        scrollbars = []
        if horizontal_scroll:
            h_scroll = ttk.Scrollbar(canvas_frame, orient="horizontal", command=canvas.xview)
            canvas.configure(xscrollcommand=h_scroll.set)
            h_scroll.grid(row=1, column=0, sticky=(tk.W, tk.E))
            scrollbars.append(h_scroll)
        
        if vertical_scroll:
            v_scroll = ttk.Scrollbar(canvas_frame, orient="vertical", command=canvas.yview)
            canvas.configure(yscrollcommand=v_scroll.set)
            v_scroll.grid(row=0, column=1, sticky=(tk.N, tk.S))
            scrollbars.append(v_scroll)
        
        canvas.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # 配置网格权重
        canvas_frame.grid_rowconfigure(0, weight=1)
        canvas_frame.grid_columnconfigure(0, weight=1)
        
        # 绑定鼠标滚轮事件
        CanvasManager._bind_mouse_events(canvas, horizontal_scroll, vertical_scroll)
        
        return canvas
    
    @staticmethod
    def _bind_mouse_events(canvas: tk.Canvas, horizontal_scroll: bool, vertical_scroll: bool):
        """绑定鼠标滚轮事件"""
        def on_mousewheel(event):
            if horizontal_scroll and vertical_scroll:
                # Shift + 滚轮 = 水平滚动，否则垂直滚动
                if event.state & 0x1:  # Shift键按下
                    canvas.xview_scroll(int(-1 * (event.delta / 120)), "units")
                else:
                    canvas.yview_scroll(int(-1 * (event.delta / 120)), "units")
            elif horizontal_scroll:
                canvas.xview_scroll(int(-1 * (event.delta / 120)), "units")
            elif vertical_scroll:
                canvas.yview_scroll(int(-1 * (event.delta / 120)), "units")
        
        def on_shift_mousewheel(event):
            if horizontal_scroll:
                canvas.xview_scroll(int(-1 * (event.delta / 120)), "units")
        
        canvas.bind("<MouseWheel>", on_mousewheel)
        canvas.bind("<Shift-MouseWheel>", on_shift_mousewheel)
        canvas.bind("<Button-4>", lambda _: canvas.yview_scroll(-1, "units") if vertical_scroll else None)
        canvas.bind("<Button-5>", lambda _: canvas.yview_scroll(1, "units") if vertical_scroll else None)
        canvas.bind("<Shift-Button-4>", lambda _: canvas.xview_scroll(-1, "units") if horizontal_scroll else None)
        canvas.bind("<Shift-Button-5>", lambda _: canvas.xview_scroll(1, "units") if horizontal_scroll else None)


class RoadDrawer:
    """路图绘制器"""
    
    @staticmethod
    def get_result_color(result: GameResult) -> str:
        """获取游戏结果对应的颜色"""
        color_map = {
            GameResult.BANKER: 'red',
            GameResult.PLAYER: 'blue',
            GameResult.TIE: 'green'
        }
        return color_map.get(result, 'black')
    
    @staticmethod
    def draw_bead_road(canvas: tk.Canvas, results: List[GameResult]):
        """绘制珠盘路 (6行一列)"""
        if not results:
            canvas.configure(scrollregion=(0, 0, 0, 0))
            return
        
        # 珠盘路参数
        rows = 6
        cell_size = 20
        margin = 10
        max_x = 0
        max_y = 0
        
        for i, result in enumerate(results):
            # 计算位置 (6行一列)
            col = i // rows
            row = i % rows
            
            x = margin + col * cell_size
            y = margin + row * cell_size
            
            # 绘制圆圈
            color = RoadDrawer.get_result_color(result)
            canvas.create_oval(x, y, x + cell_size - 2, y + cell_size - 2,
                             fill=color)
            
            max_x = max(max_x, x + cell_size)
            max_y = max(max_y, y + cell_size)
        
        # 设置滚动区域并自动滚动到最右边
        canvas.configure(scrollregion=(0, 0, max_x + margin, max_y + margin))
        canvas.xview_moveto(1.0)
    
    @staticmethod
    def draw_big_road(canvas: tk.Canvas, road_data: RoadData):
        """绘制大路图"""
        if not road_data.columns:
            canvas.configure(scrollregion=(0, 0, 0, 0))
            return
        
        max_width, max_height = RoadDrawer._draw_road_on_canvas(
            canvas, road_data.columns, cell_size=20
        )
        canvas.configure(scrollregion=(0, 0, max_width, max_height))
        # 自动滚动到最右边显示最新数据
        canvas.xview_moveto(1.0)
    
    @staticmethod
    def draw_three_column_road(canvas: tk.Canvas, road_data: RoadData):
        """绘制三珠路图并标注数字和正反"""
        if not road_data.columns:
            canvas.configure(scrollregion=(0, 0, 0, 0))
            return
        
        max_width, max_height = RoadDrawer._draw_three_column_road_with_annotations(
            canvas, road_data.columns, cell_size=15
        )
        canvas.configure(scrollregion=(0, 0, max_width, max_height))
        canvas.xview_moveto(1.0)
    
    @staticmethod
    def _draw_road_on_canvas(canvas: tk.Canvas, road_columns: List[List[GameResult]],
                           cell_size: int) -> Tuple[int, int]:
        """在画布上绘制路图（每列最多6行，一旦开始向右延伸就持续延伸）"""
        margin = 10
        max_rows = 6  # 每列最多显示6行
        max_x = 0
        max_y = 0

        # 记录每个X位置在每一行的占用情况
        occupied_positions = {}  # {(x_grid, y_row): True}

        for col_idx, column in enumerate(road_columns):
            base_x_grid = col_idx  # 基础X网格位置
            extension_row = None  # 记录开始向右延伸的行号
            extension_x_offset = 0  # 记录向右延伸的偏移量

            for row_idx, result in enumerate(column):
                if row_idx < max_rows and extension_row is None:
                    # 前6行且还没开始延伸：检查冲突，如果冲突则开始向右延伸
                    x_grid = base_x_grid
                    y_row = row_idx

                    # 检查原位置是否被占用
                    if (x_grid, y_row) in occupied_positions:
                        # 原位置被占用，开始向右延伸
                        if y_row > 0:  # 回到前一行
                            extension_row = y_row - 1
                            extension_x_offset = 1  # 从该列的右侧开始
                        else:
                            # 如果是第一行，在当前行延伸
                            extension_row = y_row
                            extension_x_offset = 1

                        # 在延伸行寻找空闲位置
                        x_grid = base_x_grid + extension_x_offset
                        y_row = extension_row
                        while (x_grid, y_row) in occupied_positions:
                            x_grid += 1
                            extension_x_offset += 1

                    # 标记这个位置被占用
                    occupied_positions[(x_grid, y_row)] = True

                    x = margin + x_grid * cell_size
                    y = margin + y_row * cell_size

                elif extension_row is not None:
                    # 已经开始延伸，后续元素都在延伸行继续向右
                    extension_x_offset += 1
                    x_grid = base_x_grid + extension_x_offset
                    y_row = extension_row

                    # 在延伸行向右寻找空闲位置
                    while (x_grid, y_row) in occupied_positions:
                        x_grid += 1
                        extension_x_offset += 1

                    # 标记这个位置被占用
                    occupied_positions[(x_grid, y_row)] = True

                    x = margin + x_grid * cell_size
                    y = margin + y_row * cell_size

                else:
                    # 第7个及以后的元素，开始在第6行向右延伸
                    if extension_row is None:
                        extension_row = max_rows - 1  # 第6行
                        extension_x_offset = row_idx - max_rows + 1
                    else:
                        extension_x_offset += 1

                    x_grid = base_x_grid + extension_x_offset
                    y_row = extension_row

                    # 在延伸行向右寻找空闲位置
                    while (x_grid, y_row) in occupied_positions:
                        x_grid += 1
                        extension_x_offset += 1

                    # 标记这个位置被占用
                    occupied_positions[(x_grid, y_row)] = True

                    x = margin + x_grid * cell_size
                    y = margin + y_row * cell_size

                # 绘制圆圈
                color = RoadDrawer.get_result_color(result)
                canvas.create_oval(x, y, x + cell_size - 2, y + cell_size - 2,
                                 fill=color)

                max_x = max(max_x, x + cell_size)
                max_y = max(max_y, y + cell_size)

        return max_x + margin, max_y + margin
    
    @staticmethod
    def _draw_three_column_road_with_annotations(canvas: tk.Canvas,
                                               road_columns: List[List[GameResult]],
                                               cell_size: int) -> Tuple[int, int]:
        """在画布上绘制三珠路图并标注数字和正反"""
        # 定义各部分的间距
        number_y = 5           # 数字位置
        type_line1_y = 20      # 正反第一行位置（正）
        type_line2_y = 33      # 正反第二行位置（反）
        circle_start_y = 45    # 圆圈开始位置
        circle_end_y = 45 + 3 * cell_size  # 圆圈结束位置
        connection_line1_y = circle_end_y + 12   # 连跳第一行位置（连）
        connection_line2_y = circle_end_y + 25  # 连跳第二行位置（跳）
        
        margin = 10  # 左边距
        max_x = 0
        max_y = 0
        
        for col_idx, column in enumerate(road_columns):
            x = margin + col_idx * cell_size
            
            # 绘制列上方的数字和正反标注
            if len(column) == 3:  # 只有满3个的列才标注
                pattern_info = PatternInfo.analyze_column(column)
                
                if pattern_info.number and pattern_info.type_text and pattern_info.connection_text:
                    text_x = x + cell_size // 2
                    
                    # 在列的上方绘制数字
                    canvas.create_text(text_x, number_y, text=str(pattern_info.number),
                                     font=("Microsoft YaHei", 10, "bold"), fill="black")
                    
                    # 在数字下方绘制正反标注
                    if pattern_info.type_text == "正反":
                        # "正反"两字分别显示在固定行，正总是在第一行，反在第二行
                        canvas.create_text(text_x, type_line1_y, text="正",
                                         font=("Microsoft YaHei", 8), fill="black")
                        canvas.create_text(text_x, type_line2_y, text="反",
                                         font=("Microsoft YaHei", 8), fill="black")
                    elif pattern_info.type_text == "正":
                        # "正"显示在第一行
                        canvas.create_text(text_x, type_line1_y, text="正",
                                         font=("Microsoft YaHei", 8), fill="black")
                    elif pattern_info.type_text == "反":
                        # "反"显示在第二行
                        canvas.create_text(text_x, type_line2_y, text="反",
                                         font=("Microsoft YaHei", 8), fill="black")

                    # 在列的下方绘制连跳标注
                    if pattern_info.connection_text == "连跳":
                        # "连跳"两字分别显示在固定行，连总是在第一行，跳在第二行
                        canvas.create_text(text_x, connection_line1_y, text="连",
                                         font=("Microsoft YaHei", 8), fill="black")
                        canvas.create_text(text_x, connection_line2_y, text="跳",
                                         font=("Microsoft YaHei", 8), fill="black")
                    elif pattern_info.connection_text == "连":
                        # "连"显示在第一行
                        canvas.create_text(text_x, connection_line1_y, text="连",
                                         font=("Microsoft YaHei", 8), fill="black")
                    elif pattern_info.connection_text == "跳":
                        # "跳"显示在第二行
                        canvas.create_text(text_x, connection_line2_y, text="跳",
                                         font=("Microsoft YaHei", 8), fill="black")
            
            # 绘制圆圈
            for row_idx, result in enumerate(column):
                y = circle_start_y + row_idx * cell_size

                color = RoadDrawer.get_result_color(result)
                canvas.create_oval(x, y, x + cell_size - 2, y + cell_size - 2,
                                 fill=color)

                max_x = max(max_x, x + cell_size)
                max_y = max(max_y, y + cell_size)
        
        return max_x + margin, max_y + margin


class CanvasFactory:
    """画布工厂类"""
    
    @staticmethod
    def create_bead_road_canvas(parent, width: int, height: int) -> tk.Canvas:
        """创建珠盘路专用画布（只有水平滚动条）"""
        return CanvasManager.create_scrollable_canvas(
            parent, width, height, horizontal_scroll=True, vertical_scroll=False
        )
    
    @staticmethod
    def create_big_road_canvas(parent, width: int, height: int) -> tk.Canvas:
        """创建大路图专用画布（只有水平滚动条，每列最多6行）"""
        return CanvasManager.create_scrollable_canvas(
            parent, width, height, horizontal_scroll=True, vertical_scroll=False
        )
    
    @staticmethod
    def create_three_column_canvas(parent, width: int, height: int) -> tk.Canvas:
        """创建三珠路专用画布（只有水平滚动条）"""
        return CanvasManager.create_scrollable_canvas(
            parent, width, height, horizontal_scroll=True, vertical_scroll=False
        )
