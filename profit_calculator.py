"""
百家乐分析器收益计算模块
实现楼梯缆逻辑、收益统计、准确率计算
"""

from typing import List, Optional
from models import GameResult, LadderState, ProfitState, ColumnPrediction, BlindGateColumnPrediction


class ProfitCalculator:
    """收益计算器"""
    
    def __init__(self):
        self.ladder_state = LadderState()
        self.profit_state = ProfitState()
    
    def update_road_profits(self, actual_result: GameResult, predictions_1: List[ColumnPrediction],
                           predictions_2: List[ColumnPrediction], predictions_3: List[ColumnPrediction],
                           blind_gate_predictions_1=None, blind_gate_predictions_2=None, blind_gate_predictions_3=None):
        """更新各条路的收益（基于各自的楼梯缆）"""

        # 检查第一条路的预测
        prediction_1 = self.get_just_confirmed_prediction_info_for_road(predictions_1, actual_result)
        if prediction_1:
            bet_amount_1 = self.ladder_state.levels[self.profit_state.ladder_level_1]
            if prediction_1['value'] == actual_result:
                # 预测正确，赢钱，楼梯缆级别下降
                self.profit_state.profit_1 += bet_amount_1
                if self.profit_state.ladder_level_1 > 0:
                    self.profit_state.ladder_level_1 -= 1
            else:
                # 预测错误，输钱，楼梯缆级别上升
                self.profit_state.profit_1 -= bet_amount_1
                if self.profit_state.ladder_level_1 < len(self.ladder_state.levels) - 1:
                    self.profit_state.ladder_level_1 += 1

        # 检查第二条路的预测
        prediction_2 = self.get_just_confirmed_prediction_info_for_road(predictions_2, actual_result)
        if prediction_2:
            bet_amount_2 = self.ladder_state.levels[self.profit_state.ladder_level_2]
            if prediction_2['value'] == actual_result:
                # 预测正确，赢钱，楼梯缆级别下降
                self.profit_state.profit_2 += bet_amount_2
                if self.profit_state.ladder_level_2 > 0:
                    self.profit_state.ladder_level_2 -= 1
            else:
                # 预测错误，输钱，楼梯缆级别上升
                self.profit_state.profit_2 -= bet_amount_2
                if self.profit_state.ladder_level_2 < len(self.ladder_state.levels) - 1:
                    self.profit_state.ladder_level_2 += 1

        # 检查第三条路的预测
        prediction_3 = self.get_just_confirmed_prediction_info_for_road(predictions_3, actual_result)
        if prediction_3:
            bet_amount_3 = self.ladder_state.levels[self.profit_state.ladder_level_3]
            if prediction_3['value'] == actual_result:
                # 预测正确，赢钱，楼梯缆级别下降
                self.profit_state.profit_3 += bet_amount_3
                if self.profit_state.ladder_level_3 > 0:
                    self.profit_state.ladder_level_3 -= 1
            else:
                # 预测错误，输钱，楼梯缆级别上升
                self.profit_state.profit_3 -= bet_amount_3
                if self.profit_state.ladder_level_3 < len(self.ladder_state.levels) - 1:
                    self.profit_state.ladder_level_3 += 1

        # 盲门预测收益完全基于正负路计算，但楼梯缆级别仍需更新（用于显示下注金额）
        self.update_blind_gate_ladder_levels(actual_result, blind_gate_predictions_1, blind_gate_predictions_2, blind_gate_predictions_3)
    
    def get_next_prediction_info_for_road(self, predictions: List[ColumnPrediction]) -> Optional[dict]:
        """获取指定路的下一个预测信息"""
        for pred_info in predictions:
            for pred in pred_info.predictions:
                if pred.actual_value is None:  # 找到第一个未确认的预测
                    return {
                        'value': pred.predicted_value,
                        'pattern': pred_info.pattern_type
                    }
        return None

    def get_just_confirmed_prediction_info_for_road(self, predictions: List[ColumnPrediction], actual_result: GameResult) -> Optional[dict]:
        """获取指定路刚刚被确认的预测信息（用于楼梯缆结算）"""
        for pred_info in predictions:
            for pred in pred_info.predictions:
                # 找到刚刚被确认的预测：actual_value等于当前结果
                if pred.actual_value == actual_result:
                    return {
                        'value': pred.predicted_value,
                        'pattern': pred_info.pattern_type
                    }
        return None

    def update_blind_gate_ladder_levels(self, actual_result: GameResult,
                                       blind_gate_predictions_1=None, blind_gate_predictions_2=None, blind_gate_predictions_3=None):
        """更新盲门预测的楼梯缆级别（不更新收益，收益基于正负路计算）"""

        # 更新第一条路的盲门楼梯缆级别
        if blind_gate_predictions_1:
            blind_gate_prediction_1 = self.get_just_confirmed_blind_gate_prediction_info_for_ladder(blind_gate_predictions_1, actual_result)
            if blind_gate_prediction_1:
                if blind_gate_prediction_1['value'] == actual_result:
                    # 预测正确，楼梯缆级别下降
                    if self.profit_state.blind_gate_ladder_level_1 > 0:
                        self.profit_state.blind_gate_ladder_level_1 -= 1
                else:
                    # 预测错误，楼梯缆级别上升
                    if self.profit_state.blind_gate_ladder_level_1 < len(self.ladder_state.levels) - 1:
                        self.profit_state.blind_gate_ladder_level_1 += 1

        # 更新第二条路的盲门楼梯缆级别
        if blind_gate_predictions_2:
            blind_gate_prediction_2 = self.get_just_confirmed_blind_gate_prediction_info_for_ladder(blind_gate_predictions_2, actual_result)
            if blind_gate_prediction_2:
                if blind_gate_prediction_2['value'] == actual_result:
                    # 预测正确，楼梯缆级别下降
                    if self.profit_state.blind_gate_ladder_level_2 > 0:
                        self.profit_state.blind_gate_ladder_level_2 -= 1
                else:
                    # 预测错误，楼梯缆级别上升
                    if self.profit_state.blind_gate_ladder_level_2 < len(self.ladder_state.levels) - 1:
                        self.profit_state.blind_gate_ladder_level_2 += 1

        # 更新第三条路的盲门楼梯缆级别
        if blind_gate_predictions_3:
            blind_gate_prediction_3 = self.get_just_confirmed_blind_gate_prediction_info_for_ladder(blind_gate_predictions_3, actual_result)
            if blind_gate_prediction_3:
                if blind_gate_prediction_3['value'] == actual_result:
                    # 预测正确，楼梯缆级别下降
                    if self.profit_state.blind_gate_ladder_level_3 > 0:
                        self.profit_state.blind_gate_ladder_level_3 -= 1
                else:
                    # 预测错误，楼梯缆级别上升
                    if self.profit_state.blind_gate_ladder_level_3 < len(self.ladder_state.levels) - 1:
                        self.profit_state.blind_gate_ladder_level_3 += 1

    def get_next_blind_gate_prediction_info_for_ladder(self, blind_gate_predictions):
        """获取下一个盲门预测信息（用于楼梯缆级别更新）"""
        for pred_info in blind_gate_predictions:
            for pred in pred_info.predictions:
                if pred.actual_value is None:  # 找到第一个未确认的预测
                    return {
                        'value': pred.predicted_value,
                        'position': pred.position
                    }
        return None

    def get_just_confirmed_blind_gate_prediction_info_for_ladder(self, blind_gate_predictions, actual_result: GameResult):
        """获取刚刚被确认的盲门预测信息（用于楼梯缆级别更新）"""
        for pred_info in blind_gate_predictions:
            for pred in pred_info.predictions:
                # 找到刚刚被确认的预测：actual_value等于当前结果
                if pred.actual_value == actual_result:
                    return {
                        'value': pred.predicted_value,
                        'position': pred.position
                    }
        return None

    def update_blind_gate_road_profit(self, actual_result: GameResult,
                                    predictions: List[BlindGateColumnPrediction], road_index: int):
        """更新指定路的盲门预测收益"""
        # 找到刚刚被确认的预测（actual_value刚被设置为actual_result的预测）
        just_confirmed_prediction = None
        for pred_info in predictions:
            for pred in pred_info.predictions:
                if pred.actual_value == actual_result and pred.predicted_value is not None:
                    # 检查这是否是刚刚被确认的预测（通过检查是否是最新的已确认预测）
                    just_confirmed_prediction = pred
                    break
            if just_confirmed_prediction:
                break

        if just_confirmed_prediction:
            # 获取对应的楼梯缆级别
            if road_index == 1:
                level = self.profit_state.blind_gate_ladder_level_1
            elif road_index == 2:
                level = self.profit_state.blind_gate_ladder_level_2
            elif road_index == 3:
                level = self.profit_state.blind_gate_ladder_level_3
            else:
                return

            bet_amount = self.ladder_state.levels[level]

            if just_confirmed_prediction.predicted_value == actual_result:
                # 预测正确，赢钱，楼梯缆级别下降
                if road_index == 1:
                    self.profit_state.blind_gate_profit_1 += bet_amount
                    if self.profit_state.blind_gate_ladder_level_1 > 0:
                        self.profit_state.blind_gate_ladder_level_1 -= 1
                elif road_index == 2:
                    self.profit_state.blind_gate_profit_2 += bet_amount
                    if self.profit_state.blind_gate_ladder_level_2 > 0:
                        self.profit_state.blind_gate_ladder_level_2 -= 1
                elif road_index == 3:
                    self.profit_state.blind_gate_profit_3 += bet_amount
                    if self.profit_state.blind_gate_ladder_level_3 > 0:
                        self.profit_state.blind_gate_ladder_level_3 -= 1
            else:
                # 预测错误，输钱，楼梯缆级别上升
                if road_index == 1:
                    self.profit_state.blind_gate_profit_1 -= bet_amount
                    if self.profit_state.blind_gate_ladder_level_1 < len(self.ladder_state.levels) - 1:
                        self.profit_state.blind_gate_ladder_level_1 += 1
                elif road_index == 2:
                    self.profit_state.blind_gate_profit_2 -= bet_amount
                    if self.profit_state.blind_gate_ladder_level_2 < len(self.ladder_state.levels) - 1:
                        self.profit_state.blind_gate_ladder_level_2 += 1
                elif road_index == 3:
                    self.profit_state.blind_gate_profit_3 -= bet_amount
                    if self.profit_state.blind_gate_ladder_level_3 < len(self.ladder_state.levels) - 1:
                        self.profit_state.blind_gate_ladder_level_3 += 1

    def get_next_blind_gate_prediction_info_for_road(self, predictions: List[BlindGateColumnPrediction]) -> Optional[dict]:
        """获取指定路的下一个盲门预测信息"""
        for pred_info in predictions:
            for pred in pred_info.predictions:
                if pred.actual_value is None:  # 找到第一个未确认的预测
                    return {
                        'value': pred.predicted_value,
                        'direction': pred.direction
                    }
        return None
    
    def generate_ladder_cable_sequence(self, positive_negative_road: List[str]) -> List[str]:
        """根据正负路生成行缆序列"""
        if not positive_negative_road:
            return []
        
        ladder_cable = []
        current_level = 0  # 从第一级开始
        
        for result in positive_negative_road:
            bet_amount = self.ladder_state.levels[current_level]
            
            if result == '+':
                # 预测正确，记录+金额，级别下降
                ladder_cable.append(f"+{bet_amount}")
                if current_level > 0:
                    current_level -= 1
            else:  # result == '-'
                # 预测错误，记录-金额，级别上升
                ladder_cable.append(f"-{bet_amount}")
                if current_level < len(self.ladder_state.levels) - 1:
                    current_level += 1
        
        return ladder_cable
    
    def auto_prediction_correct(self):
        """自动预测正确的结算"""
        # 赢了获得当前下注金额
        self.profit_state.total_profit += self.ladder_state.get_current_bet()
        
        # 如果不是在第一级，退回一级
        self.ladder_state.level_down()
    
    def auto_prediction_wrong(self):
        """自动预测错误的结算"""
        # 输了扣除当前下注金额
        self.profit_state.total_profit -= self.ladder_state.get_current_bet()
        
        # 升一级（如果还有更高级别）
        self.ladder_state.level_up()
    
    def reset_ladder(self):
        """重置楼梯缆"""
        self.ladder_state.reset()
    
    def get_bet_amount_for_road(self, road_index: int) -> int:
        """获取指定路的下注金额（已弃用，请使用get_bet_amount_from_pos_neg_road）"""
        # 这个方法已弃用，保留是为了向后兼容
        if road_index == 1:
            level = self.profit_state.ladder_level_1
        elif road_index == 2:
            level = self.profit_state.ladder_level_2
        elif road_index == 3:
            level = self.profit_state.ladder_level_3
        else:
            return 0

        return self.ladder_state.levels[level]

    def get_bet_amount_from_pos_neg_road(self, pos_neg_road: List[str]) -> int:
        """根据连跳正负路计算当前应该下注的金额"""
        if not pos_neg_road:
            return self.ladder_state.levels[0]  # 如果没有正负路，返回第一级下注金额

        # 根据正负路计算当前级别
        current_level = 0
        for result in pos_neg_road:
            if result == '+':
                # 预测正确，级别下降
                if current_level > 0:
                    current_level -= 1
            else:  # result == '-'
                # 预测错误，级别上升
                if current_level < len(self.ladder_state.levels) - 1:
                    current_level += 1

        return self.ladder_state.levels[current_level]

    def get_blind_gate_bet_amount_for_road(self, road_index: int) -> int:
        """获取指定路的盲门预测下注金额（已弃用，请使用get_blind_gate_bet_amount_from_pos_neg_road）"""
        # 这个方法已弃用，保留是为了向后兼容
        if road_index == 1:
            level = self.profit_state.blind_gate_ladder_level_1
        elif road_index == 2:
            level = self.profit_state.blind_gate_ladder_level_2
        elif road_index == 3:
            level = self.profit_state.blind_gate_ladder_level_3
        else:
            return 0

        return self.ladder_state.levels[level]

    def get_blind_gate_bet_amount_from_pos_neg_road(self, blind_gate_pos_neg_road: List[str]) -> int:
        """根据盲门正负路计算当前应该下注的金额"""
        if not blind_gate_pos_neg_road:
            return self.ladder_state.levels[0]  # 如果没有正负路，返回第一级下注金额

        # 根据正负路计算当前级别
        current_level = 0
        for result in blind_gate_pos_neg_road:
            if result == '+':
                # 预测正确，级别下降
                if current_level > 0:
                    current_level -= 1
            else:  # result == '-'
                # 预测错误，级别上升
                if current_level < len(self.ladder_state.levels) - 1:
                    current_level += 1

        return self.ladder_state.levels[current_level]

    def get_blind_gate_profit_for_road(self, road_index: int, blind_gate_pos_neg_road: List[str] = None) -> int:
        """获取指定路的盲门预测收益（完全基于正负路计算）"""
        if blind_gate_pos_neg_road:
            # 如果提供了正负路数据，直接计算行缆收益
            return self.calculate_blind_gate_profit_from_ladder_cable(blind_gate_pos_neg_road)
        else:
            # 如果正负路为空，收益必须为0
            return 0

    def calculate_blind_gate_profit_from_ladder_cable(self, blind_gate_pos_neg_road: List[str]) -> int:
        """根据盲门正负路计算行缆收益"""
        if not blind_gate_pos_neg_road:
            return 0

        total_profit = 0
        current_level = 0  # 从第一级开始

        for result in blind_gate_pos_neg_road:
            bet_amount = self.ladder_state.levels[current_level]

            if result == '+':
                # 预测正确，赢钱，级别下降
                total_profit += bet_amount
                if current_level > 0:
                    current_level -= 1
            else:  # result == '-'
                # 预测错误，输钱，级别上升
                total_profit -= bet_amount
                if current_level < len(self.ladder_state.levels) - 1:
                    current_level += 1

        return total_profit
    
    def get_profit_for_road(self, road_index: int) -> int:
        """获取指定路的收益（已弃用，请使用get_profit_from_pos_neg_road）"""
        # 这个方法已弃用，保留是为了向后兼容
        if road_index == 1:
            return self.profit_state.profit_1
        elif road_index == 2:
            return self.profit_state.profit_2
        elif road_index == 3:
            return self.profit_state.profit_3
        else:
            return 0

    def get_profit_from_pos_neg_road(self, pos_neg_road: List[str]) -> int:
        """根据连跳正负路计算收益（与行缆计算结果一致）"""
        if not pos_neg_road:
            return 0

        total_profit = 0
        current_level = 0  # 从第一级开始

        for result in pos_neg_road:
            bet_amount = self.ladder_state.levels[current_level]

            if result == '+':
                # 预测正确，赢钱，级别下降
                total_profit += bet_amount
                if current_level > 0:
                    current_level -= 1
            else:  # result == '-'
                # 预测错误，输钱，级别上升
                total_profit -= bet_amount
                if current_level < len(self.ladder_state.levels) - 1:
                    current_level += 1

        return total_profit
    
    def get_ladder_info_text(self) -> str:
        """获取楼梯缆信息文本"""
        levels_text = "-".join(map(str, self.ladder_state.levels))
        return f"楼梯缆：{levels_text}"
    
    def get_current_bet_info(self) -> dict:
        """获取当前下注信息"""
        return {
            'amount': self.ladder_state.get_current_bet(),
            'level': self.ladder_state.current_level + 1,
            'total_levels': len(self.ladder_state.levels),
            'profit': self.profit_state.total_profit
        }
    
    def get_ladder_preview(self) -> str:
        """获取投注缆预览显示"""
        preview_parts = []
        
        for i, amount in enumerate(self.ladder_state.levels):
            if i == self.ladder_state.current_level:
                # 当前级别用括号标记
                preview_parts.append(f"[{amount}]")
            else:
                preview_parts.append(str(amount))
        
        # 将所有级别用箭头连接
        preview_text = " → ".join(preview_parts)
        
        # 添加说明文字
        level_info = f"当前第{self.ladder_state.current_level + 1}级"
        return f"{level_info}  |  {preview_text}"
    
    def clear_all_profits(self):
        """清空所有收益数据"""
        self.profit_state.clear_all()
        self.ladder_state.reset()
    
    def get_profit_state(self) -> ProfitState:
        """获取收益状态"""
        return self.profit_state
    
    def get_ladder_state(self) -> LadderState:
        """获取楼梯缆状态"""
        return self.ladder_state
    
    def format_profit_text(self, profit: int) -> tuple:
        """格式化收益文本，返回(文本, 颜色)"""
        if profit > 0:
            return f"+{profit}", 'green'
        elif profit < 0:
            return str(profit), 'red'
        else:
            return "0", 'black'
    
    def format_accuracy_text(self, correct: int, total: int) -> str:
        """格式化准确率文本"""
        if total > 0:
            percentage = (correct / total) * 100
            return f"{correct}/{total} ({percentage:.1f}%)"
        else:
            return "0/0 (0%)"

    def generate_blind_gate_ladder_cable_sequence(self, positive_negative_road: List[str]) -> List[str]:
        """根据盲门正负路生成行缆序列"""
        if not positive_negative_road:
            return []

        ladder_cable = []
        current_level = 0  # 从第一级开始

        for result in positive_negative_road:
            bet_amount = self.ladder_state.levels[current_level]

            if result == '+':
                # 预测正确，记录+金额，级别下降
                ladder_cable.append(f"+{bet_amount}")
                if current_level > 0:
                    current_level -= 1
            else:  # result == '-'
                # 预测错误，记录-金额，级别上升
                ladder_cable.append(f"-{bet_amount}")
                if current_level < len(self.ladder_state.levels) - 1:
                    current_level += 1

        return ladder_cable
