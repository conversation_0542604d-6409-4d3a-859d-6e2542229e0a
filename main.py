"""
百家乐分析器主程序入口
协调各个模块，启动应用程序
"""

import tkinter as tk
from gui import BaccaratGUI


def main():
    """启动百家乐分析器"""
    try:
        # 创建主窗口
        root = tk.Tk()
        
        # 创建应用程序实例
        app = BaccaratGUI(root)
        
        # 启动主循环
        root.mainloop()
        
    except Exception as e:
        print(f"程序启动失败: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
